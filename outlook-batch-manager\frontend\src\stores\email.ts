import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Email } from '@/types/email'

export const useEmailStore = defineStore('email', () => {
  const emails = ref<Email[]>([])
  const selectedEmails = ref<string[]>([])
  const isLoading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)

  // 设置邮件列表
  const setEmails = (emailList: Email[]) => {
    emails.value = emailList
  }

  // 添加邮件
  const addEmails = (emailList: Email[]) => {
    emails.value.push(...emailList)
  }

  // 更新邮件
  const updateEmail = (emailId: string, updates: Partial<Email>) => {
    const index = emails.value.findIndex(email => email.id === emailId)
    if (index >= 0) {
      emails.value[index] = { ...emails.value[index], ...updates }
    }
  }

  // 删除邮件
  const removeEmail = (emailId: string) => {
    const index = emails.value.findIndex(email => email.id === emailId)
    if (index >= 0) {
      emails.value.splice(index, 1)
    }
  }

  // 选择邮件
  const selectEmail = (emailId: string) => {
    if (!selectedEmails.value.includes(emailId)) {
      selectedEmails.value.push(emailId)
    }
  }

  // 取消选择邮件
  const unselectEmail = (emailId: string) => {
    const index = selectedEmails.value.indexOf(emailId)
    if (index >= 0) {
      selectedEmails.value.splice(index, 1)
    }
  }

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedEmails.value.length === emails.value.length) {
      selectedEmails.value = []
    } else {
      selectedEmails.value = emails.value.map(email => email.id)
    }
  }

  // 清空选择
  const clearSelection = () => {
    selectedEmails.value = []
  }

  // 批量标记已读
  const markAsRead = (emailIds: string[]) => {
    emailIds.forEach(id => {
      updateEmail(id, { isRead: true })
    })
  }

  // 批量标记未读
  const markAsUnread = (emailIds: string[]) => {
    emailIds.forEach(id => {
      updateEmail(id, { isRead: false })
    })
  }

  // 批量删除
  const deleteEmails = (emailIds: string[]) => {
    emailIds.forEach(id => {
      removeEmail(id)
    })
  }

  return {
    emails,
    selectedEmails,
    isLoading,
    currentPage,
    pageSize,
    total,
    setEmails,
    addEmails,
    updateEmail,
    removeEmail,
    selectEmail,
    unselectEmail,
    toggleSelectAll,
    clearSelection,
    markAsRead,
    markAsUnread,
    deleteEmails
  }
})
