import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Account } from '@/types/account'

export const useAuthStore = defineStore('auth', () => {
  const accounts = ref<Account[]>([])
  const currentAccount = ref<Account | null>(null)
  const isLoading = ref(false)

  // 添加账户
  const addAccount = (account: Account) => {
    const existingIndex = accounts.value.findIndex(acc => acc.email === account.email)
    if (existingIndex >= 0) {
      accounts.value[existingIndex] = account
    } else {
      accounts.value.push(account)
    }
  }

  // 删除账户
  const removeAccount = (email: string) => {
    const index = accounts.value.findIndex(acc => acc.email === email)
    if (index >= 0) {
      accounts.value.splice(index, 1)
      if (currentAccount.value?.email === email) {
        currentAccount.value = null
      }
    }
  }

  // 设置当前账户
  const setCurrentAccount = (account: Account | null) => {
    currentAccount.value = account
  }

  // 获取账户列表
  const getAccounts = () => {
    return accounts.value
  }

  // 根据邮箱获取账户
  const getAccountByEmail = (email: string) => {
    return accounts.value.find(acc => acc.email === email)
  }

  return {
    accounts,
    currentAccount,
    isLoading,
    addAccount,
    removeAccount,
    setCurrentAccount,
    getAccounts,
    getAccountByEmail
  }
})
