import axios from 'axios'
import type { Account } from '@/types/account'

const api = axios.create({
  baseURL: '/api',
  timeout: 10000
})

// 获取Microsoft登录URL
export const getLoginUrl = async (): Promise<string> => {
  const response = await api.get('/auth/login')
  return response.data.loginUrl
}

// 获取账户列表
export const getAccounts = async (): Promise<Account[]> => {
  const response = await api.get('/accounts')
  return response.data
}

// 删除账户
export const deleteAccount = async (accountId: string): Promise<void> => {
  await api.delete(`/accounts/${accountId}`)
}

// 刷新Token
export const refreshToken = async (accountId: string): Promise<Account> => {
  const response = await api.post(`/auth/refresh/${accountId}`)
  return response.data
}
