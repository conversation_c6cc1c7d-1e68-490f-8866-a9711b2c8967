{"common": {"download": "Download", "actions": "Actions", "reset": "Reset", "enable": "Enable", "disable": "Disable", "view": "View", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "confirm": "Confirm", "refresh": "Refresh", "save": "Save", "submit": "Submit", "create": "Create", "update": "Update", "remove": "Remove", "add": "Add", "detail": "Details", "more": "More", "back": "Back", "copy": "Copy", "close": "Close", "created": "Created Time", "updated": "Created Time", "publish": "Publish"}, "首页": "Home", "分享管理": "Share Management", "收藏夹": "Favorites", "回收站": "Recycle Bin", "联系人": "Contacts", "团队管理": "Team Management", "AI助手": "AI Assistant", "team": {"management": "Team Management", "approvedTeams": "Approved Teams", "pendingTeams": "Pending Teams", "createTeam": "Create Team", "editTeam": "Edit Team", "teamNameRequired": "Please enter team name", "teamNameLength": "Team name should be 2-100 characters", "teamDescriptionLength": "Team description should be 2-500 characters", "maxMembersRequired": "Please enter max members", "inviteCodeLength": "Invite code should be 6-10 characters", "inviteCodeRequired": "Invite code is required", "maxMembersRange": "Max Members Range: 1-100", "maxMembersEditRange": "Max Members Edit Range", "createFirstTeam": "Create First Team", "teamAvatarInvalid": "Please enter a valid URL", "noTeams": "No teams yet", "managementDesc": "Create and manage your teams, invite members to collaborate", "joinTeam": "Join Team", "createTeams": "Create Team", "allTeams": "All Teams", "chatRoom": "Chat Room", "enter": "Enter", "memberProgress": "Member Progress", "created": "Created Time", "members": "Members", "searchPlaceholder": "Search team name or description", "chat": "Team Chat", "overview": "Team Overview", "role": {"owner": "Owner", "admin": "Admin", "member": "Member"}}, "titleBar": {"minimize": "Minimize", "maximize": "Maximize", "restore": "Rest<PERSON>", "close": "Close"}, "settings": {"title": "Settings", "description": "Personalize your <PERSON><PERSON><PERSON> experience", "theme": {"title": "Theme Settings", "description": "Choose your favorite theme mode and color scheme", "mode": "Theme Mode", "modeDesc": "Select light, dark, system default, or sunrise mode", "modeLight": "Light", "modeDark": "Dark", "modeAuto": "Auto", "modeSunrise": "Sunrise", "color": "Theme Color", "colorDesc": "Select your favorite primary color", "preview": "Theme Preview", "previewDesc": "Preview the current theme effect", "previewCardTitle": "Sample Card", "previewButton": "Primary Button", "previewText": "This is a sample text to demonstrate the text color and background effect of the current theme.", "previewInput": "Input placeholder"}, "other": {"title": "Other Settings", "description": "More personalization options", "language": "Language", "languageDesc": "Choose the interface language", "languagePlaceholder": "Select Language", "layout": "Layout Settings", "layoutDesc": "Choose navigation bar layout style", "layoutSidebar": "Sidebar Navigation", "layoutHorizontal": "Top Navigation", "backgroundEffect": "Background Effects", "backgroundEffectDesc": "Choose homepage background animation effects", "layoutModeSuccess": "Select effect type", "backgroundEffectNone": "No Effects", "backgroundEffectParticles": "Particle Stars", "backgroundEffectWaves": "Geometric Waves", "backgroundEffectBubbles": "Floating Bubbles", "backgroundEffectGrid": "Light Dots Grid", "backgroundEffectIntensity": "Effect Intensity", "backgroundEffectIntensityDesc": "Adjust the density and intensity of background effects", "windowAlwaysOnTop": "Always on Top", "windowAlwaysOnTopDesc": "Keep window always on top of other windows", "closeToTray": "Close to Tray", "closeToTrayDesc": "Hide to system tray when closing window instead of exiting", "minimizeToTray": "Minimize to Tray", "minimizeToTrayDesc": "Hide to system tray when minimizing window"}, "messages": {"themeModeSuccess": "Switched to {mode} mode", "themeColorSuccess": "Applied {themeName} theme", "layoutModeSuccess": "Switched to {mode} layout", "backgroundEffectSuccess": "Switched to {effect} effects", "windowAlwaysOnTopEnabled": "Always on top enabled", "windowAlwaysOnTopDisabled": "Always on top disabled", "closeToTrayEnabled": "Close to tray enabled", "closeToTrayDisabled": "Close to tray disabled", "minimizeToTrayEnabled": "Minimize to tray enabled", "minimizeToTrayDisabled": "Minimize to tray disabled", "settingFailed": "Setting failed"}, "themeOptions": {"default": "De<PERSON>ult Blue", "blue": "Tech Blue", "green": "Nature Green", "purple": "Elegant Purple", "orange": "Vibrant Orange"}, "ai": {"title": "AI Assistant Configuration", "description": "Configure your AI assistant related settings", "apiKey": "API Key", "apiKeyDesc": "Your OpenRouter API Key for calling AI services", "apiKeyPlaceholder": "Please enter your API Key", "model": "Default Model", "modelDesc": "Select the default AI model to use", "save": "Save", "test": "Test Connection", "testing": "Testing...", "saving": "Saving...", "testSuccess": "API Key test successful", "testFailed": "API Key test failed", "saveSuccess": "Configuration saved successfully", "saveFailed": "Configuration save failed", "getApiKey": "Get API Key", "getApiKeyDesc": "Visit OpenRouter official website to register and get free API Key"}}, "filesView": {"header": {"toggleSidebar": "Toggle folder tree", "searchPlaceholder": "Search files...", "advancedSearch": "Advanced Search", "sortPlaceholder": "Sort by", "sortTimeDesc": "Time↓", "sortTimeAsc": "Time↑", "sortNameDesc": "Name↓", "sortNameAsc": "Name↑", "uploading": "Uploading...", "uploadButton": "Upload(1GB Insert)", "createFolder": "New Folder", "refreshTooltip": "Refresh file list (F5)", "refresh": "Refresh", "shortcutsTooltip": "Keyboard shortcuts (Press ? to view)"}, "breadcrumbs": {"root": "All Files"}, "content": {"emptyFolder": "This folder is empty", "uploadFirstFile": "Upload your first file", "colName": "Name", "colSize": "Size", "colModified": "Modified", "colActions": "Actions", "folderIndicator": "Folder"}, "contextMenu": {"download": "Download", "share": "Share", "copy": "Copy", "cut": "Cut", "paste": "Paste", "pasteItems": "Paste ({count} items)", "rename": "<PERSON><PERSON>", "delete": "Delete", "createFolder": "New Folder"}, "dialogs": {"createFolderTitle": "New Folder", "createFolderPrompt": "Please enter the folder name", "renameTitle": "<PERSON><PERSON>", "renamePrompt": "Please enter the new file name", "deleteTitle": "Confirm Deletion", "deletePrompt": "Are you sure you want to delete \"{name}\"?", "pasteTitle": "{operation} Confirmation", "pastePrompt": "Are you sure you want to {operation} {count} items to the current folder?", "pasteConfirm": "{operation}", "shortcutsTitle": "Keyboard Shortcuts", "shortcutsConfirm": "Got it", "cancel": "Cancel"}, "messages": {"storageQuotaExceeded": "Storage space exceeded! Required: {needed}, Available: {available}", "largeFileWillChunk": "Large files will be uploaded in chunks: {files}", "largeFileHashing": "Uploading large file, calculating file hash, please wait...", "uploadSuccess": "Successfully uploaded {count} files!", "uploadFailed": "File upload failed: {message}", "getListFailed": "Failed to get file list: {status}", "networkError": "Network connection failed, please check if the backend service is running", "getListFailedGeneral": "Failed to get file list", "searchComplete": "Search complete", "searchFailed": "Search failed", "advancedSearchFound": "Found {count} matching files", "advancedSearchNotFound": "No matching files found", "searchServiceUnavailable": "Search service is currently unavailable", "uploadFeatureUnavailable": "Upload feature is temporarily unavailable", "invalidFolderName": "Folder name cannot contain special characters", "createFolderSuccess": "Folder created successfully: {name}", "createFolderFailed": "Failed to create folder", "downloadFolderNotSupported": "Downloading folders is not supported yet", "downloadStarted": "Download started: {name}", "downloadFailedUnauthorized": "Download failed: Insufficient permissions, please log in again", "downloadFailedNotFound": "Download failed: File not found", "downloadFailedGeneral": "Download failed: {message}", "shareFolderNotSupported": "Sharing folders is not supported yet", "shareCreatedSuccess": "Share link created for \"{name}\"", "invalidFileName": "File name cannot contain special characters", "renameSuccess": "Rename successful", "renameFailed": "<PERSON><PERSON> failed", "deleteSuccess": "Deletion successful", "deleteFailed": "Deletion failed", "refreshing": "Refreshing file list...", "refreshed": "Page has been refreshed", "selectionCancelled": "Selection cleared", "itemsSelected": "{count} items selected", "selectToDelete": "Please select a file or folder to delete first", "selectToPreview": "Please select a file to preview first", "selectToDownload": "Please select a file to download first", "selectToRename": "Please select a file or folder to rename first", "viewSwitched": "Switched to {view} view", "viewGrid": "Grid", "viewList": "List", "sidebarSwitched": "Sidebar has been {status}", "statusShown": "shown", "statusHidden": "hidden", "cleared": "Selection and search cleared", "selectToCopy": "Please select a file or folder to copy first", "copied": "{count} items copied", "selectToCut": "Please select a file or folder to cut first", "cut": "{count} items cut, will be moved on paste", "clipboardEmpty": "Clipboard is empty, nothing to paste", "pasteOperation": "{operation} complete", "operationCopy": "Copy", "operationMove": "Move", "dragDropFailGetFile": "Drag and drop failed: could not get dragged file info", "dragDropFailFindFile": "Drag and drop failed: could not find dragged file", "dragDropToFolderOnly": "Files can only be dropped into folders", "dragDropToSelf": "Cannot move an item into itself", "moveConfirmTitle": "Move Confirmation", "moveConfirmPrompt": "Are you sure you want to move {itemType} \"{sourceName}\" to folder \"{targetName}\"?", "itemTypeFolder": "folder", "itemTypeFile": "file", "moveConfirmButton": "Move", "moveFailed": "Move failed", "folderMoveSuccess": "Folder \"{sourceName}\" has been moved to \"{targetName}\"", "fileMoveSuccess": "File \"{sourceName}\" has been moved to \"{targetName}\"", "folderRenameSuccess": "Folder renamed successfully", "folderRenameFailed": "Failed to rename folder", "folderDeleteSuccess": "Folder deleted successfully", "folderDeleteFailed": "Failed to delete folder", "folderMoveOpFailed": "Failed to move folder", "copiedToClipboard": "Copied \"{name}\"", "cutToClipboard": "Cut \"{name}\", will be moved on paste", "previewNotSupported": "Preview is not supported for file {name}, please download to view."}}, "folderTree": {"title": "Folders", "refreshTooltip": "Refresh folder tree", "allFiles": "All Files", "rename": "<PERSON><PERSON>", "delete": "Delete", "refresh": "Refresh", "renameDialogTitle": "<PERSON><PERSON>", "folderNameLabel": "Folder Name", "folderNamePlaceholder": "Enter the new folder name", "cancel": "Cancel", "confirm": "Confirm", "deleteConfirmTitle": "Tip", "deleteConfirmText": "This will permanently delete the folder and all its contents. Continue?", "deleteSuccess": "Folder deleted successfully", "deleteFail": "Failed to delete folder", "deleteCancelled": "Deletion cancelled", "renameSuccess": "Rename successful", "renameFail": "<PERSON><PERSON> failed", "error": {"nameRequired": "Folder name cannot be empty", "nameTooLong": "Folder name cannot exceed 100 characters", "nameInvalidChars": "Folder name cannot contain special characters", "loadFailed": "Failed to load folders", "moveFailed": "Failed to move folder", "moveToChild": "Cannot move a folder into itself or one of its subfolders"}}, "advancedSearch": {"title": "Advanced Search", "fileNameLabel": "File Name", "fileNamePlaceholder": "Enter file name keywords", "fileTypeLabel": "File Type", "fileTypePlaceholder": "Select file types", "fileTypes": {"image": "Image", "imageDesc": "Image files", "video": "Video", "videoDesc": "Video files", "document": "Document", "documentDesc": "Document files", "spreadsheet": "Spreadsheet", "spreadsheetDesc": "Spreadsheet files", "pdf": "PDF", "pdfDesc": "PDF files", "archive": "Archive", "archiveDesc": "Archive files"}, "fileSizeLabel": "File Size", "minSizePlaceholder": "Min", "maxSizePlaceholder": "Max", "dateRangeLabel": "Modified Date", "dateRangeSeparator": "To", "dateRangeStartPlaceholder": "Start date", "dateRangeEndPlaceholder": "End date", "searchScopeLabel": "<PERSON> Scope", "scopes": {"current": "Current folder", "all": "All files", "folder": "Specific folder"}, "targetFolderPlaceholder": "Select a folder", "sortByLabel": "Sort By", "sortByPlaceholder": "Select sort order", "sortOptions": {"relevance": "Relevance", "time_desc": "Date modified↓", "time_asc": "Date modified↑", "name_desc": "Name↓", "name_asc": "Name↑", "size_desc": "Size↓", "size_asc": "Size↑"}, "buttons": {"reset": "Reset", "cancel": "Cancel", "search": "Search"}, "messages": {"searchInProgress": "Searching, please wait..."}}, "dashboard": {"vipCenter": {"title": "VIP Membership Center", "subtitle": "Manage your VIP membership services", "features": {"title": "VIP Premium Features", "storage": {"title": "Storage Space", "description": "Enjoy larger cloud storage space", "basicLimit": "Basic users: 5GB", "vipLimit": "VIP users: 100GB"}, "teamCreate": {"title": "Team Creation", "description": "Create and manage team collaboration", "basicLimit": "Basic users: Max 3 teams", "vipLimit": "VIP users: Unlimited teams"}, "aiReport": {"title": "AI Work Reports", "description": "Use AI to generate intelligent work reports", "basicLimit": "Basic users: 30 times per month", "vipLimit": "VIP users: Unlimited"}, "prioritySupport": {"title": "Priority Customer Support", "description": "Enjoy priority technical support services", "basicLimit": "Basic users: Standard support", "vipLimit": "VIP users: Priority support"}}, "badges": {"free": "Free", "vipExclusive": "VIP Exclusive"}, "statusCard": {"userTypes": {"vip": "VIP Member", "basic": "Basic User"}, "upgradeButton": "Upgrade VIP", "expireTime": "Expiry time: {time}", "expiringSoon": "Expiring soon", "storageSpace": "Storage Space", "featureUsage": "Feature Usage", "unlimited": "Unlimited", "upgradeVipTips": "Upgrade VIP for more privileges", "benefits": ["100GB Storage Space", "Unlimited Team Creation", "Unlimited AI Work Reports", "Priority Customer Support"]}, "upgradeDialog": {"title": "Upgrade VIP Membership", "currentStatus": "You are currently a {userType} user, VIP{daysLeft}", "selectPlan": "Select This Plan", "mostPopular": "Most Popular", "priceSymbol": "$", "originalPrice": "Original Price ${price}", "paymentMethods": "Payment Methods", "alipay": "Alipay", "orderSummary": "Order Summary", "planLabel": "Plan", "durationLabel": "Duration", "oneMonth": "1 Month", "oneYear": "1 Year", "originalPriceLabel": "Original Price", "discountLabel": "Discount", "totalAmountLabel": "Total Amount", "cancel": "Cancel", "payNow": "Pay Now ${amount}", "processing": "Processing...", "selectPlanFirst": "Please select a plan", "redirectingToPayment": "Redirecting to payment page...", "paymentFailed": "Payment failed, please try again later", "getPaymentInfoFailed": "Failed to get payment information", "selected": "Selected"}, "subscriptionManagement": {"mySubscription": "My Subscription", "upgradeVip": "Upgrade VIP", "manageSubscription": "Manage Subscription", "cancelSubscription": "Cancel Subscription", "startTime": "Start Time", "endTime": "End Time", "daysLeft": "Days Left", "actualAmount": "<PERSON><PERSON>", "noSubscription": "You don't have any VIP subscription yet", "activateVipNow": "Activate VIP Now", "subscriptionHistory": "Subscription History", "paymentRecords": "Payment Records", "orderNo": "Order No", "planType": "Plan Type", "monthlyVip": "Monthly VIP", "yearlyVip": "Yearly VIP", "amount": "Amount", "status": "Status", "paymentMethod": "Payment Method", "createTime": "Create Time", "paymentTime": "Payment Time", "cancelSubscriptionConfirm": "Cancel Subscription Confirmation", "confirmCancelSubscription": "Confirm cancellation of subscription?", "cancelWarning": "After cancellation, VIP service will stop at the end of the current billing cycle and cannot be refunded.", "cancel": "Cancel", "confirmCancel": "Confirm Cancel", "subscriptionCancelled": "Subscription cancelled", "cancelFailed": "Failed to cancel subscription", "orderStatus": {"paid": "Paid", "pending": "Pending", "failed": "Failed", "cancelled": "Cancelled"}, "paymentMethods": {"alipay": "Alipay", "wechat": "WeChat Pay", "manual": "Manual Confirmation"}, "subscriptionStatus": {"active": "Active", "expired": "Expired", "cancelled": "Cancelled", "pending": "Pending"}, "daysUnit": "days"}, "vipPayment": {"title": "VIP Payment", "back": "Back", "loadingPayment": "Loading payment page...", "paymentLoadFailed": "Payment page failed to load", "paymentInfoExpired": "Payment information has expired, please initiate payment again", "paymentSuccess": "Payment successful! Redirecting...", "paymentCancelled": "Payment cancelled", "backRetry": "Go back and retry", "processing": "Processing...", "paymentProcessFailed": "Payment processing failed, please try again"}, "paymentResult": {"success": {"title": "Payment Successful!", "message": "Congratulations, VIP membership activated successfully", "orderNo": "Order No:", "planType": "Plan Type:", "amount": "Amount Paid:", "activationTime": "Activation Time:", "viewVipStatus": "View VIP Status", "backHome": "Back to Home", "monthlyVip": "Monthly VIP", "yearlyVip": "Yearly VIP"}, "failed": {"title": "Payment Failed", "defaultMessage": "An error occurred during payment, please try again later", "retryPayment": "Retry Payment", "backHome": "Back to Home"}, "cancelled": {"title": "Payment Cancelled", "message": "You cancelled this payment, you can try again later", "retryPayment": "Retry Payment", "backHome": "Back to Home"}, "loading": {"title": "Processing...", "message": "Verifying payment result, please wait"}, "errors": {"missingOrderInfo": "Missing order information", "orderNotFound": "Order not found", "paymentFailed": "Payment failed", "verificationFailed": "Failed to verify payment result", "fetchOrderFailed": "Failed to fetch order information"}, "messages": {"vipActivatedSuccess": "VIP activated successfully!"}}}, "layout": {"themeTooltip": "Toggle theme", "userMenu": {"profile": "Profile", "settings": "Settings", "logout": "Logout"}, "sidebar": {"fileManagement": "File Management", "collaboration": "Collaboration", "aiFeatures": "AI Features", "admin": "Admin", "menus": {"home": "Home", "myFiles": "My Files", "transfer": "Transfers", "shared": "Shared", "favorites": "Favorites", "trash": "Trash", "contacts": "Contacts", "teams": "Teams", "comments": "Comments", "aiChat": "AI Assistant", "workReports": "AI Work Reports", "analytics": "Analytics", "systemManagement": "System Mgt", "rabbitmqTest": "RabbitMQ Test", "vipCenter": "VIP Center"}}, "storage": {"title": "Storage"}, "logoutConfirm": {"title": "Confirm", "text": "Are you sure you want to log out?", "confirmButton": "Confirm", "cancelButton": "Cancel"}}, "home": {"user": "User", "subtitle": "{greeting}, let's start your efficient file management journey", "greetings": {"early": "It's late", "morning": "Good morning", "forenoon": "Good morning", "noon": "Good noon", "afternoon": "Good afternoon", "evening": "Good evening"}, "quickActions": {"title": "Quick Actions", "upload": {"title": "Upload Files", "description": "Upload documents, images, videos, etc."}, "fileManage": {"title": "File Management", "description": "Browse and manage your files"}, "shareManage": {"title": "Share Management", "description": "Manage your file shares"}, "projectSpace": {"title": "Project Space", "description": "Manage development projects and team collaboration"}, "codeRepo": {"title": "Code Repository", "description": "Git repository management and version control"}, "apiDocs": {"title": "API Documentation", "description": "API documentation management and testing tools"}, "teamManagement": {"title": "Team Management", "description": "Create and manage your teams, invite members to collaborate"}, "userManage": {"title": "User Management", "description": "Manage system users and permissions"}, "systemMonitor": {"title": "System Monitor", "description": "Monitor system running status"}, "uploadDesc": "Upload documents, images, videos, etc.", "createFolder": "New Folder", "createFolderDesc": "Create new folders to organize files", "browse": "Browse Files", "browseDesc": "View and manage all your files", "search": "Search Files", "searchDesc": "Quickly find the files you need"}, "userTypes": {"basic": "Basic User", "vip": "VIP Developer", "admin": "System Administrator"}, "welcome": {"defaultUser": "User", "subtitles": {"basic": "Welcome to RickPan Cloud Drive, secure and reliable file storage and sharing platform", "vip": "Welcome to RickPan Developer Edition, enjoy professional development tools and team collaboration features", "admin": "System Administrator, you can manage users and monitor system status"}, "upgradeHint": "Upgrade to Developer Edition"}, "stats": {"fileCount": "File Count", "usedSpace": "Used Space", "shareCount": "Share Count", "files": "files", "used": "Used"}, "storageOverview": {"title": "Storage Overview", "usage": "Storage Usage", "fileTypes": "File Type Distribution", "recentActivity": "Recent Activity", "fileTypeNames": {"document": "Documents", "image": "Images", "video": "Videos", "other": "Others"}, "activities": {"upload": "Uploaded {fileName}", "share": "Shared {folderName}", "createFolder": "Created {folderName}"}, "storageUsage": {"title": "Storage Usage"}, "projectStats": {"title": "Project Statistics", "activeProjects": "Active Projects", "codeRepos": "Code Repositories", "teamMembers": "Team Members"}, "systemStats": {"title": "System Statistics", "totalUsers": "Total Users", "onlineUsers": "Online Users", "systemLoad": "System Load"}}, "files": {"recent": "Recent Files", "favorites": "Favorite Files", "viewAll": "View All", "manageFavorites": "Manage Favorites", "emptyRecent": "No recent files", "emptyFavorites": "No favorite files", "today": "Today", "yesterday": "Yesterday", "daysAgo": "{days} days ago", "recentFiles": {"title": "Recent Files", "viewAll": "View All", "empty": "No recent files"}, "favoriteFiles": {"title": "Favorite Files", "manageFavorites": "Manage Favorites", "empty": "No favorite files"}, "fileTypeDistribution": {"title": "File Type Distribution", "types": {"documents": "Documents", "images": "Images", "videos": "Videos", "others": "Others"}}}, "collaboration": {"title": "Collaboration", "membersUnit": "members", "teamMembers": "Team Members", "status": {"online": "Online", "offline": "Offline", "busy": "Busy"}, "myShares": {"title": "My Shares", "active": "Active", "expired": "Expired", "totalViews": "Total Views", "manageShares": "Manage Shares", "createShare": "Create Share"}, "teamCollaboration": {"title": "Team Collaboration", "teamManage": "Team Management", "inviteMember": "Invite Member"}, "sharedFolders": {"title": "Shared Folders", "memberCount": "{count} members", "viewAll": "View All", "createShared": "Create Shared"}}, "developer": {"title": "Developer Tools", "projectManage": {"title": "Project Management", "viewAll": "View All", "createProject": "New Project", "statuses": {"active": "In Development", "completed": "Completed", "planning": "Planning"}}, "codeRepo": {"title": "Code Repository", "viewAll": "View All", "createRepo": "New Repository", "commits": "{count} commits"}, "apiDocs": {"title": "API Documentation", "viewAll": "View All", "createDoc": "New Documentation", "version": "v{version}"}}, "admin": {"title": "System Management", "userStats": {"title": "User Statistics", "totalUsers": "Total Users", "onlineUsers": "Online Users", "vipUsers": "VIP Users", "userManage": "User Management"}, "systemMonitor": {"title": "System Monitor", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "diskUsage": "Disk Usage", "detailMonitor": "Detailed Monitor"}, "systemActivity": {"title": "System Activity", "viewLogs": "View Logs"}}, "messages": {"uploadHint": "Redirecting to file manager for upload", "createFolderHint": "Redirecting to file manager to create folder", "searchHint": "Redirecting to file manager for search", "viewFile": "Viewing file: {fileName}", "enterSharedFolder": "Entering shared folder: {folderName}", "upgradeVip": "VIP upgrade feature coming soon, stay tuned!", "projectSpaceDev": "Project space feature under development...", "codeRepoDev": "Code repository feature under development...", "apiDocsDev": "API documentation feature under development...", "userManageDev": "User management feature under development...", "systemMonitorDev": "System monitoring feature under development...", "teamManageDev": "Team management feature under development...", "inviteMemberDev": "Invite member feature under development...", "createSharedFolderDev": "Create shared folder feature under development...", "createProjectDev": "New project feature under development...", "createRepoDev": "New repository feature under development...", "createApiDocDev": "New API documentation feature under development...", "systemLogsDev": "System logs feature under development...", "loadDataError": "Failed to load data"}}, "share": {"title": "Share Management", "nav": {"all": "All Shares", "active": "Active", "expired": "Expired", "disabled": "Disabled"}, "stats": {"title": "Share Statistics", "total": "Total Shares", "active": "Active", "expired": "Expired", "totalViews": "Total Views", "todayViews": "Today's Views"}, "filter": {"searchPlaceholder": "Search shared files", "statusPlaceholder": "Status", "startDatePlaceholder": "Start Date", "endDatePlaceholder": "End Date", "clearFilter": "Clear Filter", "refresh": "Refresh", "statusOptions": {"all": "All", "active": "Active", "expired": "Expired", "disabled": "Disabled"}, "dateShortcuts": {"today": "Today", "yesterday": "Yesterday", "lastWeek": "Last Week", "lastMonth": "Last Month", "last3Months": "Last 3 Months"}}, "list": {"columns": {"fileName": "File Name", "shareCode": "Share Code", "accessCount": "Access Count", "status": "Status", "expiryTime": "Expiry Time", "createdTime": "Created Time", "actions": "Actions"}, "status": {"active": "Active", "expired": "Expired", "disabled": "Disabled"}, "actions": {"view": "View", "copy": "Copy Link", "disable": "Disable", "enable": "Enable", "delete": "Delete", "qrcode": "QR Code"}, "empty": {"title": "No Share Records", "description": "You haven't created any file shares yet", "action": "Create Share"}, "expiryTime": {"permanent": "Permanent", "expired": "Expired", "expiresIn": "Expires in {time}"}}, "detail": {"title": "Share Details", "basicInfo": {"title": "Basic Information", "fileName": "File Name", "fileSize": "File Size", "shareCode": "Share Code", "shareLink": "Share Link", "password": "Access Password", "noPassword": "No Password", "expiryTime": "Expiry Time", "createdTime": "Created Time", "status": "Status"}, "accessStats": {"title": "Access Statistics", "totalAccess": "Total Access Count", "todayAccess": "Today's Access", "lastAccessTime": "Last Access Time", "neverAccessed": "Never Accessed"}, "accessLog": {"title": "Access Log", "columns": {"accessTime": "Access Time", "ipAddress": "IP Address", "userAgent": "User Agent", "location": "Location"}, "empty": "No access records"}, "actions": {"copyLink": "Copy Link", "copyCode": "Copy Share Code", "generateQR": "Generate QR Code", "disable": "Disable Share", "enable": "Enable Share", "delete": "Delete Share", "edit": "Edit Share"}}, "messages": {"copySuccess": "<PERSON><PERSON>d successfully", "copyFailed": "Co<PERSON> failed", "disableSuccess": "Share disabled", "disableFailed": "Disable failed", "enableSuccess": "Share enabled", "enableFailed": "Enable failed", "deleteSuccess": "Share deleted", "deleteFailed": "Delete failed", "deleteConfirm": "Are you sure you want to delete this share?", "deleteConfirmContent": "This action cannot be undone and the share link will become invalid", "loadFailed": "Failed to load share data", "operationFailed": "Operation failed, please try again later"}}, "profile": {"header": {"title": "Profile", "subtitle": "Manage your personal information and account settings"}, "card": {"changeAvatar": "Change Avatar", "roleAdmin": "Admin", "roleUser": "User", "statusActive": "Active", "statusDisabled": "Disabled", "storageUsed": "Used Space", "storageQuota": "Total Space", "usage": "Usage"}, "tabs": {"basic": "Basic Info", "security": "Security", "storage": "Storage", "preferences": "Preferences"}, "avatarDialog": {"title": "Change Avatar", "drag_text_1": "Click or drag image here", "drag_text_2": "Supports JPG, PNG, GIF format, up to 2MB", "preview": "Avatar Preview", "cancel": "Cancel", "confirm": "Confirm Upload", "error_format": "Only image files can be uploaded!", "error_size": "Image size cannot exceed 2MB!", "error_select": "Please select an avatar image first", "success": "Avatar updated successfully", "fail": "Failed to upload avatar"}, "basicInfo": {"title": "Basic Information", "subtitle": "Manage your basic personal information", "username": "Username", "username_tip_1": "Username cannot be changed", "username_tip_2": "Username is your unique identifier and cannot be changed after registration", "realName": "Real Name", "realName_placeholder": "Enter your real name", "email": "Email Address", "email_placeholder": "Enter your email address", "email_tip": "Changing your email requires verification of the new address", "phone": "Phone Number", "phone_placeholder": "Enter your phone number", "bio": "Bio", "bio_placeholder": "Enter your bio (optional)", "registeredAt": "Registered At", "updatedAt": "Last Updated", "reset": "Reset", "save": "Save Changes", "rules": {"realName_max": "Real name cannot exceed 50 characters", "email_required": "Please enter your email address", "email_format": "Please enter a valid email format", "phone_format": "Please enter a valid phone number", "bio_max": "Bio cannot exceed 200 characters"}, "messages": {"reset_confirm_title": "Confirm Reset", "reset_confirm_text": "Are you sure you want to reset all changes? Unsaved changes will be lost.", "reset_success": "Form has been reset", "no_change": "No changes to save", "update_success": "Profile updated successfully", "update_fail": "Failed to update profile"}, "unknown": "Unknown", "format_error": "Format Error"}, "security": {"title": "Security Settings", "subtitle": "Manage your account security and password settings", "changePassword": "Change Password", "currentPassword": "Current Password", "currentPassword_placeholder": "Enter current password", "newPassword": "New Password", "newPassword_placeholder": "Enter new password", "passwordStrength": "Password Strength:", "strength": {"weak": "Weak", "medium": "Medium", "strong": "Strong", "very_strong": "Very Strong"}, "confirmPassword": "Confirm New Password", "confirmPassword_placeholder": "Enter new password again", "changePassword_button": "Change Password", "info": "Security Information", "lastLoginTime": "Last Login Time:", "lastLoginIP": "Last Login IP:", "passwordLastModified": "Password Last Modified:", "daysAgo": "{days} days ago", "suggestions": "Security Suggestions", "suggestion_title": "For your account security, it is recommended to change your password regularly", "suggestion_1": "Password must be at least 8 characters long and include uppercase and lowercase letters, numbers, and special characters", "suggestion_2": "Do not use passwords related to personal information", "suggestion_3": "It is recommended to change your password every 3 months", "suggestion_4": "Do not use the same password on multiple websites", "rules": {"current_required": "Please enter your current password", "new_required": "Please enter a new password", "new_min": "Password must be at least 8 characters long", "new_pattern": "Password must contain uppercase and lowercase letters, numbers, and special characters", "confirm_required": "Please confirm your new password", "confirm_match": "The two passwords do not match"}, "messages": {"success": "Password changed successfully", "fail": "Failed to change password"}}, "storage": {"title": "Storage Management", "subtitle": "View and manage your storage space usage", "overview": "Storage Usage", "used_percentage": "{percentage}% used", "fileTypeDistribution": "File Type Distribution", "file_unit": "files", "file_types": {"document": "Documents", "image": "Images", "video": "Videos", "audio": "Audio", "archive": "Archives"}, "largestFiles": "Largest Files by <PERSON><PERSON>", "table": {"name": "Name", "size": "Size", "type": "Type", "modified": "Modified", "actions": "Actions"}, "delete": "Delete", "cleanup": "Cleanup Suggestions", "suggestions": {"duplicates_title": "Clean up duplicate files", "duplicates_text": "Found {count} duplicate files, freeing up {size} of space", "start_cleanup": "Start Cleanup", "trash_title": "Empty Trash", "trash_text": "{count} files in trash, freeing up {size} of space", "empty_trash": "Empty Trash", "temp_title": "Clean up temporary files", "temp_text": "Clean up temporary files and cache generated during upload", "clear_cache": "<PERSON>ache"}, "delete_confirm": {"title": "Confirm Deletion", "text": "Are you sure you want to delete \"{name}\"? This action cannot be undone.", "success": "File deleted successfully"}}, "preferences": {"title": "Preference Settings", "subtitle": "Personalize your user experience and interface preferences", "interface": "Interface Preferences", "themeMode": "Theme Mode", "themeMode_desc": "Choose your preferred interface theme", "modes": {"light": "Light Mode", "dark": "Dark Mode", "auto": "System Default"}, "themeColor": "Theme Color", "themeColor_desc": "Choose your favorite primary color", "language": "Language", "language_desc": "Choose the display language for the interface", "languages": {"zh_CN": "简体中文", "en_US": "English", "zh_TW": "繁體中文"}, "functional": "Functional Preferences", "defaultView": "Default View Mode", "defaultView_desc": "The default display mode for the file list", "views": {"grid": "Grid View", "list": "List View"}, "autoSave": "Auto Save", "autoSave_desc": "Automatically save user preference settings", "showHidden": "Show Hidden Files", "showHidden_desc": "Show hidden files in the file list", "notifications": "Notification Settings", "email_title": "Email Notifications", "email_desc": "Receive email notifications for important actions", "browser_title": "Browser Notifications", "browser_desc": "Receive browser push notifications", "share_title": "File Share Notifications", "share_desc": "Notify when someone shares a file with you", "storage_title": "Storage Space Warning", "storage_desc": "Remind when storage space is low", "actions": {"reset": "<PERSON><PERSON>", "save": "Save Settings"}, "messages": {"save_success": "Preferences saved successfully", "save_fail": "Failed to save preferences", "reset_confirm_title": "Confirm Reset", "reset_confirm_text": "Are you sure you want to restore all settings to their default values?", "reset_success": "Default settings have been restored"}}}}, "transfer": {"title": "Transfer List", "header": {"refresh": "Refresh", "clearHistory": "Clear History"}, "socket": {"title": "Real-time Transfers"}, "stats": {"inProgress": "In Progress", "completed": "Completed", "failed": "Failed", "total": "Total"}, "transferDialogs": {"cancel_title": "Confirm Cancel", "cancel_text": "Are you sure you want to cancel the transfer of {name}?"}, "connection": {"connecting_title": "Connecting to real-time service", "connecting_desc": "Establishing real-time connection, please wait...", "disconnected_title": "Real-time connection lost", "disconnected_desc": "Transfer progress will not update in real-time. Please check your network connection.", "reconnect": "Reconnect", "connected_title": "Real-time connection is active"}, "current": {"title": "Current Transfers", "empty": "No ongoing transfer tasks", "pause": "Pause", "resume": "Resume", "cancel": "Cancel", "retry": "Retry", "calculating": "Calculating..."}, "upload": {"title": "Upload Tasks"}, "download": {"title": "Download Tasks"}, "history": {"title": "Transfer History", "empty": "No transfer history records", "filter_all": "All", "filter_completed": "Completed", "filter_failed": "Failed", "table": {"fileName": "File Name", "size": "Size", "status": "Status", "completedAt": "Completed At", "actions": "Actions"}, "delete": "Delete", "retry": "Retry"}, "status": {"PENDING": "Pending", "IN_PROGRESS": "In Progress", "COMPLETED": "Completed", "FAILED": "Failed", "CANCELLED": "Cancelled", "PAUSED": "Paused"}, "dialogs": {"clear_title": "Confirm Action", "clear_text": "Are you sure you want to clear all transfer history? This action cannot be undone.", "cancel_title": "Confirm Cancel", "cancel_text": "Are you sure you want to cancel the transfer of {name}?", "delete_title": "Confirm Deletion", "delete_text": "Are you sure you want to delete {name} from the history?"}, "messages": {"refresh_ok": "Transfer list has been refreshed", "history_cleared": "Transfer history has been cleared", "cancel_wip": "Cancel transfer feature is under development...", "pause_wip": "Pause feature is under development...", "resume_wip": "Resume feature is under development...", "delete_ok": "History record has been deleted", "retry_wip": "Retry feature is under development..."}}, "trash": {"title": "Trash", "stats": {"totalFiles": "files", "totalSize": "Total size", "expiringSoon": "expiring soon", "autoCleanup": "Auto cleanup after 30 days"}, "toolbar": {"selectAll": "Select All", "batchRestore": "<PERSON><PERSON>", "batchDelete": "<PERSON><PERSON> Delete", "emptyTrash": "Empty Trash", "refresh": "Refresh", "sortBy": "Sort by"}, "sort": {"deletedAt_desc": "Deleted Time↓", "deletedAt_asc": "Deleted Time↑", "originalName_desc": "File Name↓", "originalName_asc": "File Name↑", "fileSize_desc": "File Size↓", "fileSize_asc": "File Size↑"}, "fileList": {"fileName": "File Name", "deletedTime": "Deleted Time", "size": "Size", "actions": "Actions", "restore": "Rest<PERSON>", "permanentDelete": "Delete Permanently", "deletedAt": "Deleted at", "folder": "Folder", "expiringSoon": "Expiring soon"}, "dialogs": {"restoreTitle": "Restore Files", "restoreFilesCount": "Files to restore ({count})", "restoreLocation": "Restore Location", "restoreToOriginal": "Restore to original location", "restoreToRoot": "Restore to root directory", "restoreToOriginalDesc": "Files will be restored to the folder they were deleted from. If the original folder doesn't exist, they will be restored to the root directory", "restoreToRootDesc": "All files will be restored to the root directory", "restoreConfirm": "Confirm <PERSON>ore", "deleteTitle": "Delete Files Permanently", "deleteWarning": "Warning: This action cannot be undone", "deleteDescription": "You are about to permanently delete the following files. This action will:", "deletePoint1": "Permanently delete file data from the server", "deletePoint2": "Cannot be recovered by any means", "deletePoint3": "Free up the corresponding storage space", "deleteConfirmText": "Please confirm that you really want to perform this operation!", "deleteConfirm": "Delete Permanently", "deleteFilesCount": "Files to delete permanently ({count})", "fileCount": "File Count:", "totalSize": "Total Size:", "expiringSoonLabel": "Expiring Soon:", "expiringSoonFiles": "{count} files", "confirmDeleteText": "Please type", "confirmDeleteKeyword": "CONFIRM DELETE", "confirmDeletePlaceholder": "Please type: CONFIRM DELETE", "confirmDeletePrompt": "to confirm this operation:", "emptyTrashTitle": "Empty Trash", "emptyTrashPrompt": "Are you sure you want to empty the trash? This will permanently delete all {count} files and cannot be undone.", "emptyTrashConfirm": "Confirm Empty", "conflictInfo": "File name conflict handling", "conflictDesc": "If a file with the same name already exists at the restore location, the system will automatically rename it (e.g., filename(1).txt)", "cancel": "Cancel"}, "messages": {"emptyState": "Trash is empty", "goUpload": "Go upload files", "restoreSuccess": "Successfully restored {count} files", "restoreFailed": "Failed to restore files", "deleteSuccess": "Successfully deleted {count} files", "deleteFailed": "Failed to delete files", "emptyTrashSuccess": "Trash has been emptied", "emptyTrashFailed": "Failed to empty trash", "selectFiles": "Please select files to operate on first", "loadFailed": "Failed to load trash files", "refreshed": "Trash has been refreshed", "selectedFiles": "Selected {selected} / {total} files", "selectedSize": ", total {size}"}, "time": {"expiryTime": "{days} days until expiry", "expired": "Expired", "justNow": "Just now", "minutesAgo": "{minutes} minutes ago", "hoursAgo": "{hours} hours ago", "daysAgo": "{days} days ago"}}, "favorites": {"title": "Favorites", "sidebar": {"favoriteFiles": "Favorite Files", "allFavorites": "All Favorites", "recentFavorites": "Recent Favorites", "documentType": "Documents", "imageType": "Images", "videoType": "Videos", "otherType": "Others", "myNotes": "My Notes", "allNotes": "All Notes", "recentNotes": "Recently Edited", "favoriteNotes": "Favorite Notes", "reminderNotes": "To-Do Items", "tagManagement": "Tag Management", "fileTags": "File Tags", "noteTags": "Note Tags", "customGroups": "Custom Groups"}, "toolbar": {"searchPlaceholder": "Search favorite files or notes...", "selectCategory": "Select Category", "allCategories": "All Categories", "listView": "List", "gridView": "Grid", "refresh": "Refresh", "newNote": "New Note", "more": "More", "import": "Import Notes", "export": "Export Notes", "batchDelete": "<PERSON><PERSON> Delete", "settings": "Settings"}, "categories": {"default": "<PERSON><PERSON><PERSON>", "work": "Work", "study": "Study", "life": "Life"}, "actions": {"preview": "Preview", "download": "Download", "edit": "Edit", "share": "Share", "remove": "Remove from Favorites", "delete": "Delete", "copy": "Copy", "move": "Move", "export": "Export", "favorite": "Add to Favorites", "unfavorite": "Remove from Favorites"}, "messages": {"removeSuccess": "Removed from favorites successfully", "removeFailed": "Failed to remove from favorites", "updateSuccess": "Updated successfully", "updateFailed": "Update failed", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "loadFailed": "Failed to load data", "loadFilesFailed": "Failed to load favorite files", "loadNotesFailed": "Failed to load notes", "createSuccess": "Created successfully", "createFailed": "Create failed", "saveSuccess": "Saved successfully", "saveFailed": "Save failed", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "importInDevelopment": "Import feature in development...", "exportInDevelopment": "Export feature in development...", "batchDeleteInDevelopment": "Batch delete feature in development...", "settingsInDevelopment": "Settings feature in development..."}, "folders": {"defaultGroup": "Default Group"}, "notes": {"createTitle": "Create Note", "emptyState": "No notes yet", "createFirst": "Create your first note", "title": "Title", "folder": "Folder", "tags": "Tags", "updateTime": "Updated", "viewCount": "Views", "actions": "Actions", "noContent": "No content", "noTags": "No tags", "edit": "Edit", "copy": "Copy", "export": "Export", "delete": "Delete", "more": "More", "preview": "Preview", "favorite": "Favorite", "unfavorite": "Unfavorite"}, "dialogs": {"close": "Close", "cancel": "Cancel", "confirm": "Confirm", "detail": "Note Details", "createdAt": "Created: ", "updatedAt": "Updated: ", "folderLabel": "Folder: ", "editNote": "Edit Note", "toggleFavorite": "Toggle Favorite", "exportMarkdown": "Export Markdown", "renderedView": "Rendered View", "sourceView": "Source View", "exportSuccess": "Export successful", "exportFailed": "Export failed", "unknownError": "Unknown error", "reminderTime": "Reminder: ", "editTitle": "Edit Note", "createTitle": "Create Note", "titlePlaceholder": "Enter note title", "selectFolder": "Select Folder", "defaultFolder": "Default Group", "favorited": "Favorited", "addToFavorite": "Add to Favorites", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen", "fullscreenEdit": "Fullscreen Edit", "exitFullscreenEdit": "Exit Fullscreen", "addTag": "Add Tag", "setReminder": "<PERSON>minder", "save": "Save", "saveAndContinue": "Save & Continue", "contentPlaceholder": "Start writing your note content...", "preview": "Preview", "edit": "Edit", "hasUnsavedChanges": "You have unsaved changes. Are you sure you want to close?", "confirmClose": "Confirm Close", "shortcuts": "Shortcuts: ", "shortcutSave": "Ctrl+S Save", "shortcutSaveClose": "Ctrl+Enter Save & Close", "shortcutClose": "Esc Close", "selectTags": "Select Tags", "createTag": "Create Tag", "reminderLabel": "Reminder Time", "selectReminderTime": "Select reminder time"}, "tags": {"fileTab": "File Tags", "noteTab": "Note Tags", "createFileTag": "Create File Tag", "createNoteTag": "Create Note Tag", "totalTags": "Total Tags", "totalUsage": "Total Usage", "noFileTags": "No file tags yet", "noNoteTags": "No note tags yet", "createFirstFileTag": "Create your first file tag", "createFirstNoteTag": "Create your first note tag", "usageCount": "Usage Count", "editTag": "Edit Tag", "deleteTag": "Delete Tag", "tagName": "Tag Name", "tagColor": "Tag Color", "tagNamePlaceholder": "Enter tag name", "createTagTitle": "Create Tag", "editTagTitle": "Edit Tag", "deleteConfirm": "Are you sure you want to delete this tag?", "deleteWarning": "This action cannot be undone"}, "files": {"emptyState": "No favorite files yet", "goToFiles": "Go to file management to favorite files", "fileName": "File Name", "category": "Category", "notes": "Notes", "favoriteTime": "Favorite Time", "fileSize": "File Size", "actions": "Actions", "preview": "Preview", "download": "Download", "share": "Share", "editNotes": "Edit Notes", "removeFavorite": "Remove from Favorites", "batchRemove": "<PERSON><PERSON>move from Favorites", "selectAll": "Select All", "selectedCount": "{count} items selected"}, "detail": {"title": "File Details", "basicInfo": "Basic Information", "favoriteInfo": "Favorite Information", "fileSize": "File Size:", "fileType": "File Type:", "uploadTime": "Upload Time:", "modifyTime": "Modified Time:", "favoriteTime": "Favorite Time:", "category": "Category:", "notes": "Notes:", "noNotes": "No notes", "actions": "Actions", "preview": "Preview", "download": "Download", "share": "Share", "editNotes": "Edit Notes", "removeFavorite": "Remove from Favorites"}}, "aiChat": {"title": "AI Assistant", "subtitle": "Intelligent conversation assistant for professional answers", "clearChat": "Clear Chat", "settings": "Settings", "thinking": "AI is thinking...", "inputPlaceholder": "Enter your question...", "noApiKeyPlaceholder": "Please configure API Key in settings first", "noApiKeyTitle": "API Key Not Configured", "noApiKeyDesc": "Please go to settings page to configure your OpenRouter API Key to use AI assistant", "goToSettings": "Go to Settings", "sendMessage": "Send", "retry": "Retry", "copy": "Copy", "copied": "<PERSON>pied", "user": "Me", "assistant": "AI Assistant", "error": "Error occurred", "networkError": "Network connection failed, please check network and retry", "apiError": "API call failed, please check API Key configuration", "unknownError": "Unknown error, please try again later", "welcomeMessage": "Hello! I'm your AI assistant, how can I help you?", "modelSelector": {"title": "Select Model", "current": "Current Model"}, "feedbackLike": "Thank you for your feedback!", "feedbackDislike": "Thank you for your feedback, we'll keep improving!", "roleSelector": {"selectRole": "Select AI Role", "customRole": "Custom Role", "manageRoles": "Manage Roles", "exportRoles": "Export Roles", "importRoles": "Import Roles", "switchedTo": "Switched to {name}"}, "roleCustom": {"title": "Custom AI Role", "name": "Role Name", "description": "Role Description", "category": "Role Category", "avatar": "Role Avatar", "prompt": "System Prompt", "preview": "Role Preview", "create": "Create Role", "cancel": "Cancel", "nameRequired": "Please enter role name", "descRequired": "Please enter role description", "promptRequired": "Please enter system prompt", "createSuccess": "Role created successfully"}}, "project": {"kanban": "Project Kanban", "taskManagement": "Task Management", "projects": "Projects", "createProject": "Create Project", "editProject": "Edit Project", "deleteProject": "Delete Project", "name": "Project Name", "namePlaceholder": "Enter project name", "nameRequired": "Project name is required", "nameLength": "Project name should be 2-100 characters", "description": "Description", "descriptionPlaceholder": "Enter project description", "descriptionLength": "Description cannot exceed 500 characters", "priorityPlaceholder": "Select priority", "priorityRequired": "Priority is required", "startDate": "Start Date", "startDatePlaceholder": "Select start date", "endDate": "End Date", "endDatePlaceholder": "Select end date", "endDateError": "End date cannot be earlier than start date", "manager": "Project Manager", "managerPlaceholder": "Select project manager", "progress": "Progress", "status": {"planning": "Planning", "active": "Active", "completed": "Completed", "archived": "Archived", "todo": "To Do", "inProgress": "In Progress", "review": "Review", "cancelled": "Cancelled"}, "noPlanningProjects": "No planning projects", "noActiveProjects": "No active projects", "noCompletedProjects": "No completed projects", "noArchivedProjects": "No archived projects", "noProjects": "No projects", "fetchError": "Failed to fetch projects", "statusUpdateSuccess": "Project status updated successfully", "statusUpdateError": "Failed to update project status", "createSuccess": "Project created successfully", "createError": "Failed to create project", "updateSuccess": "Project updated successfully", "updateError": "Failed to update project", "deleteConfirmTitle": "Delete Project", "deleteConfirmMessage": "Are you sure you want to delete project {name}? This action cannot be undone.", "deleteSuccess": "Project deleted successfully", "deleteError": "Failed to delete project", "overdue": "Overdue", "nearDeadline": "Near Deadline", "tasks": "Tasks", "announcements": "Announcements", "priority": {"description": "Priority", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "createTask": "Create Task", "editTask": "Edit Task", "taskTitle": "Task Title", "taskTitlePlaceholder": "Enter task title", "taskTitleRequired": "Task title is required", "taskTitleLength": "Task title should be 2-200 characters", "taskDescription": "Task Description", "taskDescriptionPlaceholder": "Enter task description", "taskDescriptionLength": "Task description cannot exceed 1000 characters", "assignee": "Assignee", "assigneePlaceholder": "Select assignee", "unassigned": "Unassigned", "dueDate": "Due Date", "dueDatePlaceholder": "Select due date", "estimatedHours": "Estimated Hours", "estimatedHoursPlaceholder": "Enter estimated hours", "estimatedHoursRange": "Estimated hours should be between 0-999", "actualHours": "Actual Hours", "actualHoursPlaceholder": "Enter actual hours", "actualHoursRange": "Actual hours should be between 0-999", "taskStatus": "Task Status", "completedAt": "Completed At", "taskProgress": "Task Progress", "completed": "Completed", "hours": "Hours", "estimated": "Estimated", "actual": "Actual", "taskTags": "Task Tags", "taskTagsPlaceholder": "Enter or select tags", "taskDependencies": "Task Dependencies", "taskDependenciesPlaceholder": "Select dependent tasks", "tags": {"frontend": "Frontend", "backend": "Backend", "database": "Database", "ui": "UI", "api": "API", "testing": "Testing", "documentation": "Documentation", "bugfix": "Bug Fix", "feature": "Feature", "optimization": "Optimization"}, "selectProject": "Select Project", "allStatus": "All Status", "allAssignees": "All Assignees", "searchTasks": "Search Tasks", "totalTasks": "Total Tasks", "todoTasks": "To Do Tasks", "inProgressTasks": "In Progress Tasks", "completedTasks": "Completed Tasks", "selectProjectFirst": "Please select a project first", "noTasks": "No tasks", "createFirstTask": "Create First Task", "fetchTasksError": "Failed to fetch tasks", "createTaskSuccess": "Task created successfully", "createTaskError": "Failed to create task", "updateTaskSuccess": "Task updated successfully", "updateTaskError": "Failed to update task", "deleteTaskConfirmTitle": "Delete Task", "deleteTaskConfirmMessage": "Are you sure you want to delete task {title}? This action cannot be undone.", "deleteTaskSuccess": "Task deleted successfully", "deleteTaskError": "Failed to delete task", "taskStatusUpdateSuccess": "Task status updated successfully", "taskStatusUpdateError": "Failed to update task status", "taskAssignSuccess": "Task assigned successfully", "taskAssignError": "Failed to assign task", "createAnnouncement": "Create Announcement", "editAnnouncement": "Edit Announcement", "announcementTitle": "Announcement Title", "announcementTitlePlaceholder": "Enter announcement title", "announcementTitleRequired": "Announcement title is required", "announcementTitleLength": "Announcement title should be 2-200 characters", "announcementContent": "Announcement Content", "announcementContentPlaceholder": "Enter announcement content", "announcementContentRequired": "Announcement content is required", "announcementContentMinLength": "Announcement content should be at least 10 characters", "pinAnnouncement": "Pin Announcement", "unpinAnnouncement": "Unpin Announcement", "pinAnnouncementHint": "Pinned announcements will be displayed at the top of the list", "write": "Write", "preview": "Preview", "markdownSupported": "<PERSON><PERSON> supported, ", "markdownGuide": "View Markdown Guide", "noContentToPreview": "No content to preview", "publish": "Publish", "pinned": "Pinned", "pinnedAnnouncements": "Pinned Announcements", "regularAnnouncements": "Regular Announcements", "unknownUser": "Unknown User", "showMore": "Show More", "showLess": "Show Less", "allTime": "All Time", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "searchAnnouncements": "Search Announcements", "totalAnnouncements": "Total Announcements", "todayAnnouncements": "Today's Announcements", "weekAnnouncements": "This Week's Announcements", "monthAnnouncements": "This Month's Announcements", "noAnnouncements": "No announcements", "createFirstAnnouncement": "Create First Announcement", "fetchAnnouncementsError": "Failed to fetch announcements", "createAnnouncementSuccess": "Announcement created successfully", "createAnnouncementError": "Failed to create announcement", "updateAnnouncementSuccess": "Announcement updated successfully", "updateAnnouncementError": "Failed to update announcement", "deleteAnnouncementConfirmTitle": "Delete Announcement", "deleteAnnouncementConfirmMessage": "Are you sure you want to delete announcement {title}? This action cannot be undone.", "deleteAnnouncementSuccess": "Announcement deleted successfully", "deleteAnnouncementError": "Failed to delete announcement", "pinAnnouncementSuccess": "Announcement pinned successfully", "pinAnnouncementError": "Failed to pin announcement", "unpinAnnouncementSuccess": "Announcement unpinned successfully", "unpinAnnouncementError": "Failed to unpin announcement"}}