{"name": "outlook-batch-manager-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "@microsoft/microsoft-graph-client": "^3.0.7", "@azure/msal-browser": "^3.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.10", "vue-tsc": "^1.8.25", "typescript": "~5.3.0", "@types/node": "^20.10.5", "eslint": "^8.56.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}}