# Outlook邮箱批量管理工具 - 项目总体规划文档

## 📋 项目概述

### 项目名称
Outlook Batch Manager - 多账户邮箱批量管理工具

### 项目背景
用户拥有多个Outlook邮箱账户，需要一个统一的工具来批量管理这些邮箱，避免频繁在浏览器中切换登录不同账户的繁琐操作。

### 项目目标
- 🎯 **统一管理**: 在一个界面中管理多个Outlook邮箱账户
- 🚀 **批量操作**: 支持批量接收、发送、标记、删除邮件
- 🔒 **安全认证**: 使用Microsoft Graph API官方认证，无需存储密码
- 💻 **跨平台**: 基于Web技术，支持多平台访问

## 🛠 技术架构

### 技术栈选择
- **前端**: Vue3 + TypeScript + SCSS + Element Plus
- **后端**: Node.js + Express + TypeScript
- **认证**: Microsoft Graph API + OAuth2
- **数据库**: SQLite (本地存储账户配置)
- **状态管理**: Pinia
- **HTTP客户端**: Axios

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue3 前端     │    │  Node.js 后端   │    │ Microsoft Graph │
│                 │    │                 │    │      API        │
│ - 用户界面      │◄──►│ - API路由       │◄──►│                 │
│ - 状态管理      │    │ - 认证处理      │    │ - 邮件服务      │
│ - 邮件展示      │    │ - Token管理     │    │ - 用户认证      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎨 功能模块设计

### 1. 账户管理模块
- **添加账户**: OAuth2授权流程添加新的Outlook账户
- **账户列表**: 显示已添加的所有账户及状态
- **账户删除**: 移除不需要的账户
- **Token刷新**: 自动刷新过期的访问令牌

### 2. 邮件管理模块
- **统一收件箱**: 聚合显示所有账户的邮件
- **邮件列表**: 支持分页、排序、筛选
- **邮件详情**: 查看邮件内容、附件
- **批量操作**: 标记已读/未读、删除、移动文件夹

### 3. 邮件发送模块
- **撰写邮件**: 富文本编辑器
- **发件人选择**: 选择使用哪个账户发送
- **收件人管理**: 支持多收件人、抄送、密送
- **附件上传**: 支持多文件附件

### 4. 搜索与过滤模块
- **全局搜索**: 跨所有账户搜索邮件
- **高级筛选**: 按发件人、主题、日期等筛选
- **标签管理**: 自定义标签分类邮件

## 🔐 认证与安全

### OAuth2认证流程
1. **应用注册**: 在Microsoft Azure注册应用
2. **授权请求**: 用户授权访问邮箱
3. **Token获取**: 获取访问令牌和刷新令牌
4. **Token存储**: 安全存储在本地数据库
5. **Token刷新**: 自动刷新过期令牌

### 安全措施
- 🔐 Token加密存储
- 🕒 Token自动过期处理
- 🛡️ HTTPS通信
- 🔒 CORS安全配置

## 📊 数据库设计

### 账户表 (accounts)
```sql
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(255),
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 邮件缓存表 (emails_cache)
```sql
CREATE TABLE emails_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id INTEGER,
    message_id VARCHAR(255),
    subject TEXT,
    sender VARCHAR(255),
    received_date DATETIME,
    is_read BOOLEAN DEFAULT FALSE,
    body_preview TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);
```

## 🚀 开发计划

### Phase 1: 基础架构搭建 (1-2天)
- [x] 项目初始化
- [ ] 前后端基础框架搭建
- [ ] Microsoft Graph API集成
- [ ] OAuth2认证实现

### Phase 2: 核心功能开发 (3-4天)
- [ ] 账户管理功能
- [ ] 邮件获取与展示
- [ ] 基础邮件操作

### Phase 3: 高级功能 (2-3天)
- [ ] 邮件发送功能
- [ ] 搜索与过滤
- [ ] 批量操作优化

### Phase 4: 优化与测试 (1-2天)
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 用户体验优化

## 📁 项目结构

```
outlook-batch-manager/
├── doc/                          # 文档目录
│   └── 项目总体规划文档.md
├── frontend/                     # Vue3前端
│   ├── src/
│   │   ├── components/          # 组件
│   │   ├── views/              # 页面
│   │   ├── stores/             # Pinia状态管理
│   │   ├── api/                # API接口
│   │   └── utils/              # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── backend/                      # Node.js后端
│   ├── src/
│   │   ├── routes/             # 路由
│   │   ├── controllers/        # 控制器
│   │   ├── services/           # 服务层
│   │   ├── models/             # 数据模型
│   │   └── middleware/         # 中间件
│   ├── package.json
│   └── tsconfig.json
└── README.md
```

## 🔧 环境配置

### Microsoft Azure应用注册
1. 访问 [Azure Portal](https://portal.azure.com)
2. 注册新应用程序
3. 配置重定向URI
4. 获取Client ID和Client Secret
5. 设置API权限：
   - `Mail.Read`
   - `Mail.Send`
   - `Mail.ReadWrite`
   - `User.Read`

### 环境变量配置
```env
# Microsoft Graph API
MICROSOFT_CLIENT_ID=your_client_id
MICROSOFT_CLIENT_SECRET=your_client_secret
MICROSOFT_REDIRECT_URI=http://localhost:3000/auth/callback

# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库
DATABASE_PATH=./data/database.sqlite
```

## 📈 性能优化策略

### 前端优化
- 🔄 虚拟滚动处理大量邮件列表
- 📦 组件懒加载
- 🗂️ 邮件内容缓存
- ⚡ 防抖搜索

### 后端优化
- 🔄 批量API调用
- 📊 邮件增量同步
- 🗃️ Token缓存机制
- ⏱️ 请求限流

## 🎯 用户体验设计

### 界面设计原则
- 🎨 **简洁明了**: 清晰的信息层级
- 🚀 **响应迅速**: 快速的操作反馈
- 📱 **响应式**: 适配不同屏幕尺寸
- 🔍 **易于搜索**: 强大的搜索功能

### 交互流程
1. **首次使用**: 引导用户添加第一个账户
2. **日常使用**: 快速查看和处理邮件
3. **批量操作**: 高效的多选和批量处理
4. **错误处理**: 友好的错误提示和恢复建议

## 📝 总结

本项目旨在为用户提供一个高效、安全、易用的多Outlook账户管理工具。通过现代化的Web技术栈和Microsoft官方API，确保功能的稳定性和安全性。项目采用模块化设计，便于后续功能扩展和维护。
