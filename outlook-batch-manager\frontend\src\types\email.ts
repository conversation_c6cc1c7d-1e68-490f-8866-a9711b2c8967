export interface Email {
  id: string
  messageId: string
  accountId: string
  accountEmail: string
  subject: string
  sender: {
    name: string
    email: string
  }
  recipients: {
    name: string
    email: string
  }[]
  bodyPreview: string
  body?: string
  isRead: boolean
  hasAttachments: boolean
  receivedDateTime: Date
  importance: 'low' | 'normal' | 'high'
  categories: string[]
  conversationId?: string
}

export interface EmailAttachment {
  id: string
  name: string
  contentType: string
  size: number
  isInline: boolean
  contentId?: string
}

export interface SendEmailRequest {
  accountEmail: string
  subject: string
  body: string
  bodyType: 'text' | 'html'
  toRecipients: string[]
  ccRecipients?: string[]
  bccRecipients?: string[]
  attachments?: File[]
  importance?: 'low' | 'normal' | 'high'
}
