{"common": {"download": "下载", "actions": "操作", "reset": "重置", "enable": "开启", "disable": "关闭", "view": "查看", "edit": "编辑", "delete": "删除", "cancel": "取消", "confirm": "确认", "refresh": "刷新", "save": "保存", "submit": "提交", "create": "创建", "update": "更新", "remove": "移除", "add": "添加", "detail": "详情", "more": "更多", "back": "返回", "copy": "复制", "created": "创建时间", "close": "关闭", "updated": "创建时间", "publish": "发布"}, "titleBar": {"minimize": "最小化", "maximize": "最大化", "restore": "还原", "close": "关闭"}, "首页": "首页", "分享管理": "分享管理", "收藏夹": "收藏夹", "回收站": "回收站", "联系人": "联系人", "团队管理": "团队管理", "AI助手": "AI助手", "settings": {"title": "设置", "description": "个性化您的RickPan体验", "theme": {"title": "主题设置", "description": "选择您喜欢的主题模式和配色方案", "mode": "主题模式", "modeDesc": "选择日间、夜间、跟随系统或日出日落模式", "modeLight": "日间", "modeDark": "夜间", "modeAuto": "跟随系统", "modeSunrise": "日出日落", "color": "主题色彩", "colorDesc": "选择您喜欢的主色调"}, "other": {"title": "其他设置", "description": "更多个性化选项", "language": "语言设置", "languageDesc": "选择界面语言", "languagePlaceholder": "选择语言", "layout": "布局设置", "layoutDesc": "选择导航栏布局方式", "layoutSidebar": "左侧导航", "layoutHorizontal": "上方导航", "backgroundEffect": "背景动效", "backgroundEffectDesc": "选择首页背景动画效果", "backgroundEffectPlaceholder": "选择动效类型", "backgroundEffectNone": "无动效", "backgroundEffectParticles": "粒子星空", "backgroundEffectBubbles": "浮动气泡", "backgroundEffectGrid": "游动光蛇", "backgroundEffectIntensity": "动效强度", "backgroundEffectIntensityDesc": "选择动效强度", "windowAlwaysOnTop": "窗口置顶", "windowAlwaysOnTopDesc": "设置窗口是否始终显示在最前面", "closeToTray": "关闭到托盘", "closeToTrayDesc": "关闭窗口时隐藏到系统托盘而不是退出应用", "minimizeToTray": "最小化到托盘", "minimizeToTrayDesc": "最小化窗口时隐藏到系统托盘"}, "themeOptions": {"default": "默认蓝", "blue": "科技蓝", "green": "自然绿", "purple": "优雅紫", "orange": "活力橙"}, "ai": {"title": "AI助手配置", "description": "配置您的AI助手相关设置", "apiKey": "API Key", "apiKeyDesc": "您的智谱AI API Key，用于调用GLM模型服务", "apiKeyPlaceholder": "请输入您的智谱AI API Key", "model": "默认模型", "modelDesc": "选择默认使用的GLM模型", "save": "保存", "test": "测试连接", "testing": "测试中...", "saving": "保存中...", "testSuccess": "API Key测试成功", "testFailed": "API Key测试失败", "saveSuccess": "配置保存成功", "saveFailed": "配置保存失败", "getApiKey": "获取API Key", "getApiKeyDesc": "前往智谱AI官网注册并获取API Key"}, "messages": {"themeModeSuccess": "主题模式切换成功", "testSuccess": "API Key测试成功", "testFailed": "API Key测试失败", "saveSuccess": "配置保存成功", "saveFailed": "配置保存失败", "windowAlwaysOnTopEnabled": "窗口置顶已开启", "windowAlwaysOnTopDisabled": "窗口置顶已关闭", "closeToTrayEnabled": "关闭到托盘已开启", "closeToTrayDisabled": "关闭到托盘已关闭", "minimizeToTrayEnabled": "最小化到托盘已开启", "minimizeToTrayDisabled": "最小化到托盘已关闭", "settingFailed": "设置失败"}}, "filesView": {"header": {"toggleSidebar": "切换文件夹树", "searchPlaceholder": "搜索文件...", "advancedSearch": "高级搜索", "sortPlaceholder": "排序方式", "sortTimeDesc": "时间↓", "sortTimeAsc": "时间↑", "sortNameDesc": "名称↓", "sortNameAsc": "名称↑", "uploading": "上传中...", "uploadButton": "上传文件(1GB以内)", "createFolder": "新建文件夹", "refreshTooltip": "刷新文件列表 (F5)", "refresh": "刷新", "shortcutsTooltip": "键盘快捷键 (按 ? 查看)"}, "breadcrumbs": {"root": "全部文件"}, "content": {"emptyFolder": "此文件夹为空", "uploadFirstFile": "上传第一个文件", "colName": "名称", "colSize": "大小", "colModified": "修改时间", "colActions": "操作", "folderIndicator": "文件夹"}, "contextMenu": {"download": "下载", "share": "分享", "copy": "复制", "cut": "剪切", "paste": "粘贴", "pasteItems": "粘贴 ({count}项)", "rename": "重命名", "delete": "删除", "createFolder": "新建文件夹"}, "dialogs": {"createFolderTitle": "新建文件夹", "createFolderPrompt": "请输入文件夹名称", "renameTitle": "重命名", "renamePrompt": "请输入新的文件名", "deleteTitle": "删除确认", "deletePrompt": "确定要删除 \"{name}\" 吗？", "pasteTitle": "{operation}确认", "pastePrompt": "确定要{operation} {count} 个项目到当前文件夹吗？", "pasteConfirm": "确定{operation}", "shortcutsTitle": "键盘快捷键帮助", "shortcutsConfirm": "知道了", "cancel": "取消"}, "messages": {"storageQuotaExceeded": "存储空间不足！需要 {needed}, 可用 {available}", "largeFileWillChunk": "文件已加入传输列表: {files}", "largeFileHashing": "文件已加入传输列表，正在准备上传...", "uploadSuccess": "成功上传 {count} 个文件！", "uploadFailed": "文件上传失败: {message}", "getListFailed": "获取文件列表失败: {status}", "networkError": "网络连接失败，请检查后端服务是否启动", "getListFailedGeneral": "获取文件列表失败", "searchComplete": "搜索完成", "searchFailed": "搜索失败", "advancedSearchFound": "找到 {count} 个匹配的文件", "advancedSearchNotFound": "未找到匹配的文件", "searchServiceUnavailable": "搜索服务暂不可用", "uploadFeatureUnavailable": "上传功能暂时不可用", "invalidFolderName": "文件夹名称不能包含特殊字符", "createFolderSuccess": "文件夹创建成功: {name}", "createFolderFailed": "创建文件夹失败", "downloadFolderNotSupported": "暂不支持下载文件夹", "downloadStarted": "开始下载: {name}", "downloadFailedUnauthorized": "下载失败: 权限不足，请重新登录", "downloadFailedNotFound": "下载失败: 文件不存在", "downloadFailedGeneral": "下载失败: {message}", "shareFolderNotSupported": "暂不支持分享文件夹", "shareCreatedSuccess": "文件 \"{name}\" 分享创建成功", "invalidFileName": "文件名不能包含特殊字符", "renameSuccess": "重命名成功", "renameFailed": "重命名失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "refreshing": "正在刷新文件列表...", "refreshed": "页面已刷新", "selectionCancelled": "已取消全选", "itemsSelected": "已选中 {count} 个项目", "selectToDelete": "请先点击选择要删除的文件或文件夹", "selectToPreview": "请先点击选择要预览的文件", "selectToDownload": "请先点击选择要下载的文件", "selectToRename": "请先点击选择要重命名的文件或文件夹", "viewSwitched": "已切换到{view}视图", "viewGrid": "网格", "viewList": "列表", "sidebarSwitched": "侧边栏已{status}", "statusShown": "显示", "statusHidden": "隐藏", "cleared": "已清除选择和搜索", "selectToCopy": "请先选择要复制的文件或文件夹", "copied": "已复制 {count} 个项目", "selectToCut": "请先选择要剪切的文件或文件夹", "cut": "已剪切 {count} 个项目，粘贴时将移动到目标位置", "clipboardEmpty": "剪贴板为空，没有可粘贴的项目", "pasteOperation": "{operation}完成", "operationCopy": "复制", "operationMove": "移动", "dragDropFailGetFile": "拖拽操作失败：无法获取被拖拽的文件信息", "dragDropFailFindFile": "拖拽操作失败：无法找到被拖拽的文件", "dragDropToFolderOnly": "只能将文件拖拽到文件夹中", "dragDropToSelf": "不能将文件移动到自己中", "moveConfirmTitle": "移动确认", "moveConfirmPrompt": "确定要将{itemType} \"{sourceName}\" 移动到文件夹 \"{targetName}\" 中吗？", "itemTypeFolder": "文件夹", "itemTypeFile": "文件", "moveConfirmButton": "确定移动", "moveFailed": "移动失败", "folderMoveSuccess": "文件夹 \"{sourceName}\" 已移动到 \"{targetName}\"", "fileMoveSuccess": "文件 \"{sourceName}\" 已移动到 \"{targetName}\"", "folderRenameSuccess": "文件夹重命名成功", "folderRenameFailed": "重命名失败", "folderDeleteSuccess": "文件夹删除成功", "folderDeleteFailed": "删除失败", "folderMoveOpFailed": "移动文件夹失败", "copiedToClipboard": "已复制 \"{name}\"", "cutToClipboard": "已剪切 \"{name}\"，粘贴时将移动到目标位置", "previewNotSupported": "文件 {name} 暂不支持预览，请下载后查看"}}, "files": {"title": "文件", "buttons": {"uploadSuccess": "文件 '{fileName}' 上传成功", "uploadFail": "文件 '{fileName}' 上传失败"}}, "folderTree": {"title": "文件夹", "refreshTooltip": "刷新文件夹树", "allFiles": "全部文件", "rename": "重命名", "delete": "删除", "refresh": "刷新", "renameDialogTitle": "重命名文件夹", "folderNameLabel": "文件夹名称", "folderNamePlaceholder": "请输入新的文件夹名称", "cancel": "取消", "confirm": "确定", "deleteConfirmTitle": "提示", "deleteConfirmText": "此操作将永久删除该文件夹及其所有内容, 是否继续?", "deleteSuccess": "成功删除文件夹", "deleteFail": "删除文件夹失败", "deleteCancelled": "已取消删除", "renameSuccess": "重命名成功", "renameFail": "重命名失败", "error": {"nameRequired": "文件夹名称不能为空", "nameTooLong": "文件夹名称不能超过100个字符", "nameInvalidChars": "文件夹名称不能包含特殊字符", "loadFailed": "加载文件夹失败", "moveFailed": "移动文件夹失败", "moveToChild": "不能将文件夹移动到其自身或其子文件夹下"}}, "advancedSearch": {"title": "高级搜索", "fileNameLabel": "文件名", "fileNamePlaceholder": "输入文件名关键词", "fileTypeLabel": "文件类型", "fileTypePlaceholder": "选择文件类型", "fileTypes": {"image": "图片", "imageDesc": "图片文件", "video": "视频", "videoDesc": "视频文件", "document": "文档", "documentDesc": "文档文件", "spreadsheet": "表格", "spreadsheetDesc": "表格文件", "pdf": "PDF", "pdfDesc": "PDF文件", "archive": "压缩包", "archiveDesc": "压缩文件"}, "fileSizeLabel": "文件大小", "minSizePlaceholder": "最小", "maxSizePlaceholder": "最大", "dateRangeLabel": "修改时间", "dateRangeSeparator": "至", "dateRangeStartPlaceholder": "开始时间", "dateRangeEndPlaceholder": "结束时间", "searchScopeLabel": "搜索范围", "scopes": {"current": "当前文件夹", "all": "全部文件", "folder": "指定文件夹"}, "targetFolderPlaceholder": "选择文件夹", "sortByLabel": "排序方式", "sortByPlaceholder": "选择排序方式", "sortOptions": {"relevance": "相关度", "time_desc": "修改时间↓", "time_asc": "修改时间↑", "name_desc": "文件名↓", "name_asc": "文件名↑", "size_desc": "文件大小↓", "size_asc": "文件大小↑"}, "buttons": {"reset": "重置", "cancel": "取消", "search": "搜索"}, "messages": {"searchInProgress": "正在搜索，请稍候..."}}, "dashboard": {"vipCenter": {"title": "VIP会员中心", "subtitle": "管理您的VIP会员服务", "features": {"title": "VIP特权功能", "storage": {"title": "存储空间", "description": "享受更大的云存储空间", "basicLimit": "基础用户: 5GB", "vipLimit": "VIP用户: 100GB"}, "teamCreate": {"title": "团队创建", "description": "创建和管理团队协作", "basicLimit": "基础用户: 最多3个团队", "vipLimit": "VIP用户: 无限团队"}, "aiReport": {"title": "AI工作报告", "description": "使用AI生成智能工作报告", "basicLimit": "基础用户: 每月30次", "vipLimit": "VIP用户: 无限制"}, "prioritySupport": {"title": "优先客服支持", "description": "享受优先技术支持服务", "basicLimit": "基础用户: 标准支持", "vipLimit": "VIP用户: 优先支持"}}, "badges": {"free": "免费", "vipExclusive": "VIP专享"}, "statusCard": {"userTypes": {"vip": "VIP会员", "basic": "基础用户"}, "upgradeButton": "升级VIP", "expireTime": "到期时间: {time}", "expiringSoon": "即将到期", "storageSpace": "存储空间", "featureUsage": "功能使用情况", "unlimited": "无限制", "upgradeVipTips": "升级VIP获得更多权限", "benefits": ["100GB 存储空间", "无限团队创建", "AI工作报告无限制", "优先客服支持"]}, "upgradeDialog": {"title": "升级VIP会员", "currentStatus": "您当前是{userType}用户，VIP{daysLeft}", "selectPlan": "选择此套餐", "mostPopular": "最受欢迎", "priceSymbol": "¥", "originalPrice": "原价 ¥{price}", "paymentMethods": "支付方式", "alipay": "支付宝", "orderSummary": "订单摘要", "planLabel": "套餐", "durationLabel": "时长", "oneMonth": "1个月", "oneYear": "1年", "originalPriceLabel": "原价", "discountLabel": "优惠", "totalAmountLabel": "应付金额", "cancel": "取消", "payNow": "立即支付 ¥{amount}", "processing": "处理中...", "selectPlanFirst": "请选择套餐", "redirectingToPayment": "正在跳转到支付页面...", "paymentFailed": "支付失败，请稍后重试", "getPaymentInfoFailed": "获取支付信息失败", "selected": "已选择"}, "subscriptionManagement": {"mySubscription": "我的订阅", "upgradeVip": "升级VIP", "manageSubscription": "管理订阅", "cancelSubscription": "取消订阅", "startTime": "开始时间", "endTime": "到期时间", "daysLeft": "剩余天数", "actualAmount": "实付金额", "noSubscription": "您还没有VIP订阅", "activateVipNow": "立即开通VIP", "subscriptionHistory": "订阅历史", "paymentRecords": "支付记录", "orderNo": "订单号", "planType": "套餐类型", "monthlyVip": "月度VIP", "yearlyVip": "年度VIP", "amount": "金额", "status": "状态", "paymentMethod": "支付方式", "createTime": "创建时间", "paymentTime": "支付时间", "cancelSubscriptionConfirm": "取消订阅确认", "confirmCancelSubscription": "确认取消订阅？", "cancelWarning": "取消后将在当前计费周期结束时停止VIP服务，无法退款。", "cancel": "取消", "confirmCancel": "确认取消", "subscriptionCancelled": "订阅已取消", "cancelFailed": "取消订阅失败", "orderStatus": {"paid": "已支付", "pending": "待支付", "failed": "支付失败", "cancelled": "已取消"}, "paymentMethods": {"alipay": "支付宝", "wechat": "微信支付", "manual": "手动确认"}, "subscriptionStatus": {"active": "生效中", "expired": "已过期", "cancelled": "已取消", "pending": "待生效"}, "daysUnit": "天"}, "vipPayment": {"title": "VIP支付", "back": "返回", "loadingPayment": "正在加载支付页面...", "paymentLoadFailed": "支付页面加载失败", "paymentInfoExpired": "支付信息已过期，请重新发起支付", "paymentSuccess": "支付成功！正在跳转...", "paymentCancelled": "支付已取消", "backRetry": "返回重试", "processing": "处理中...", "paymentProcessFailed": "支付处理失败，请重试"}, "paymentResult": {"success": {"title": "支付成功！", "message": "恭喜您，VIP会员开通成功", "orderNo": "订单号：", "planType": "套餐类型：", "amount": "支付金额：", "activationTime": "开通时间：", "viewVipStatus": "查看VIP状态", "backHome": "返回首页", "monthlyVip": "月度VIP", "yearlyVip": "年度VIP"}, "failed": {"title": "支付失败", "defaultMessage": "支付过程中出现问题，请稍后重试", "retryPayment": "重新支付", "backHome": "返回首页"}, "cancelled": {"title": "支付已取消", "message": "您取消了本次支付，可以稍后再试", "retryPayment": "重新支付", "backHome": "返回首页"}, "loading": {"title": "处理中...", "message": "正在验证支付结果，请稍候"}, "errors": {"missingOrderInfo": "缺少订单信息", "orderNotFound": "订单不存在", "paymentFailed": "支付失败", "verificationFailed": "验证支付结果失败", "fetchOrderFailed": "获取订单信息失败"}, "messages": {"vipActivatedSuccess": "VIP开通成功！"}}}, "layout": {"themeTooltip": "切换主题", "userMenu": {"profile": "个人资料", "settings": "设置", "logout": "退出登录"}, "sidebar": {"fileManagement": "文件管理", "collaboration": "协作功能", "aiFeatures": "AI功能", "admin": "管理功能", "menus": {"home": "首页", "myFiles": "我的文件", "transfer": "传输列表", "shared": "共享文件", "favorites": "收藏夹", "trash": "回收站", "contacts": "联系人", "teams": "团队空间", "comments": "评论", "aiChat": "AI助手", "workReports": "工作报告", "vipCenter": "会员中心", "analytics": "数据分析", "systemManagement": "系统管理", "teamApproval": "团队审核", "rabbitmqTest": "RabbitMQ压测"}}, "storage": {"title": "存储空间"}, "logoutConfirm": {"title": "提示", "text": "确定要退出登录吗？", "confirmButton": "确定", "cancelButton": "取消"}}, "home": {"user": "用户", "subtitle": "{greeting}，开始高效的文件管理之旅", "greetings": {"early": "夜深了", "morning": "早上好", "forenoon": "上午好", "noon": "中午好", "afternoon": "下午好", "evening": "晚上好"}, "stats": {"files": "个文件", "used": "已使用", "fileCount": "文件数量", "usedSpace": "已用空间", "shareCount": "分享数量"}, "quickActions": {"title": "快速操作", "upload": {"title": "上传文件", "description": "上传文档、图片、视频等文件"}, "fileManage": {"title": "文件管理", "description": "浏览和管理您的文件"}, "shareManage": {"title": "分享管理", "description": "管理您的文件分享"}, "teamManagement": {"title": "团队管理", "description": "创建和管理您的团队，邀请成员协作"}, "projectSpace": {"title": "团队空间", "description": "管理开发项目和团队协作"}, "codeRepo": {"title": "代码仓库", "description": "Git仓库管理和版本控制"}, "apiDocs": {"title": "API文档", "description": "API文档管理和测试工具"}, "userManage": {"title": "用户管理", "description": "管理系统用户和权限"}, "systemMonitor": {"title": "系统监控", "description": "监控系统运行状态"}, "uploadDesc": "上传文档、图片、视频等文件", "createFolder": "新建文件夹", "createFolderDesc": "创建新的文件夹来组织文件", "browse": "浏览文件", "browseDesc": "查看和管理所有文件", "search": "搜索文件", "searchDesc": "快速找到需要的文件"}, "userTypes": {"basic": "普通用户", "vip": "VIP开发者", "admin": "系统管理"}, "welcome": {"defaultUser": "用户", "subtitles": {"basic": "欢迎使用RickPan云盘，安全可靠的文件存储和分享平台", "vip": "欢迎使用RickPan开发者版，享受专业的开发工具和团队协作功能", "admin": "系统管理员，您可以管理用户和监控系统运行状态"}, "upgradeHint": "升级到开发者版"}, "additionalStats": {"fileCount": "文件数量", "usedSpace": "已用空间", "shareCount": "分享数量"}, "additionalActions": {"title": "快速操作", "upload": {"title": "上传文件", "description": "快速上传文件到云盘"}, "fileManage": {"title": "文件管理", "description": "浏览和管理您的文件"}, "shareManage": {"title": "分享管理", "description": "管理您的文件分享"}, "projectSpace": {"title": "项目空间", "description": "管理开发项目和团队协作"}, "codeRepo": {"title": "代码仓库", "description": "Git仓库管理和版本控制"}, "apiDocs": {"title": "API文档", "description": "API文档管理和测试工具"}, "userManage": {"title": "用户管理", "description": "管理系统用户和权限"}, "systemMonitor": {"title": "系统监控", "description": "系统性能和运行状态监控"}}, "storageOverview": {"title": "存储概览", "usage": "存储使用情况", "fileTypes": "文件类型分布", "recentActivity": "最近活动", "fileTypeNames": {"document": "文档", "image": "图片", "video": "视频", "other": "其他"}, "activities": {"upload": "上传了 {fileName}", "share": "分享了 {folderName}", "createFolder": "创建了 {folderName}"}, "storageUsage": {"title": "存储使用情况"}, "projectStats": {"title": "项目统计", "activeProjects": "活跃项目", "codeRepos": "代码仓库", "teamMembers": "团队成员"}, "systemStats": {"title": "系统统计", "totalUsers": "总用户数", "onlineUsers": "在线用户", "systemLoad": "系统负载"}}, "files": {"recent": "最近文件", "favorites": "收藏文件", "viewAll": "查看全部", "manageFavorites": "管理收藏", "emptyRecent": "暂无最近文件", "emptyFavorites": "暂无收藏文件", "today": "今天", "yesterday": "昨天", "daysAgo": "{days}天前", "recentFiles": {"title": "最近文件", "viewAll": "查看全部", "empty": "暂无最近文件"}, "favoriteFiles": {"title": "收藏文件", "manageFavorites": "管理收藏", "empty": "暂无收藏文件"}, "fileTypeDistribution": {"title": "文件类型分布", "types": {"documents": "文档", "images": "图片", "videos": "视频", "others": "其他"}}}, "collaboration": {"title": "团队协作", "membersUnit": "人", "teamMembers": "团队成员", "status": {"online": "在线", "offline": "离线", "busy": "忙碌"}, "myShares": {"title": "我的分享", "active": "生效中", "expired": "已过期", "totalViews": "总访问量", "manageShares": "管理分享", "createShare": "创建分享"}, "teamCollaboration": {"title": "团队协作", "teamManage": "团队管理", "inviteMember": "邀请成员"}, "sharedFolders": {"title": "共享文件夹", "memberCount": "{count}人", "viewAll": "查看全部", "createShared": "创建共享"}}, "developer": {"title": "开发者工具", "projectManage": {"title": "项目管理", "viewAll": "查看全部", "createProject": "新建项目", "statuses": {"active": "开发中", "completed": "已完成", "planning": "规划中"}}, "codeRepo": {"title": "代码仓库", "viewAll": "查看全部", "createRepo": "新建仓库", "commits": "{count}次提交"}, "apiDocs": {"title": "API文档", "viewAll": "查看全部", "createDoc": "新建文档", "version": "v{version}"}}, "admin": {"title": "系统管理", "userStats": {"title": "用户统计", "totalUsers": "总用户", "onlineUsers": "在线用户", "vipUsers": "VIP用户", "userManage": "用户管理"}, "systemMonitor": {"title": "系统监控", "cpuUsage": "CPU使用率", "memoryUsage": "内存使用率", "diskUsage": "磁盘使用率", "detailMonitor": "详细监控"}, "systemActivity": {"title": "系统活动", "viewLogs": "查看日志"}}, "messages": {"uploadHint": "跳转到文件管理页面进行上传", "createFolderHint": "跳转到文件管理页面创建文件夹", "searchHint": "跳转到文件管理页面进行搜索", "viewFile": "查看文件: {fileName}", "enterSharedFolder": "进入共享文件夹: {folderName}", "upgradeVip": "VIP升级功能即将上线，敬请期待！", "projectSpaceDev": "项目空间功能开发中...", "codeRepoDev": "代码仓库功能开发中...", "apiDocsDev": "API文档功能开发中...", "userManageDev": "用户管理功能开发中...", "systemMonitorDev": "系统监控功能开发中...", "teamManageDev": "团队管理功能开发中...", "inviteMemberDev": "邀请成员功能开发中...", "createSharedFolderDev": "创建共享文件夹功能开发中...", "createProjectDev": "新建项目功能开发中...", "createRepoDev": "新建仓库功能开发中...", "createApiDocDev": "新建API文档功能开发中...", "systemLogsDev": "系统日志功能开发中...", "loadDataError": "加载数据失败"}}, "share": {"title": "分享管理", "nav": {"all": "全部分享", "active": "生效中", "expired": "已过期", "disabled": "已禁用"}, "stats": {"title": "分享统计", "total": "总分享数", "active": "生效中", "expired": "已过期", "totalViews": "总访问量", "todayViews": "今日访问"}, "filter": {"searchPlaceholder": "搜索分享文件", "statusPlaceholder": "状态", "startDatePlaceholder": "开始日期", "endDatePlaceholder": "结束日期", "clearFilter": "清空筛选", "refresh": "刷新", "statusOptions": {"all": "全部", "active": "生效中", "expired": "已过期", "disabled": "已禁用"}, "dateShortcuts": {"today": "今天", "yesterday": "昨天", "lastWeek": "最近一周", "lastMonth": "最近一个月", "last3Months": "最近三个月"}}, "list": {"columns": {"fileName": "文件名", "shareCode": "分享码", "accessCount": "访问次数", "status": "状态", "expiryTime": "过期时间", "createdTime": "创建时间", "actions": "操作"}, "status": {"active": "生效中", "expired": "已过期", "disabled": "已禁用"}, "actions": {"view": "查看", "copy": "复制链接", "disable": "禁用", "enable": "启用", "delete": "删除", "qrcode": "二维码"}, "empty": {"title": "暂无分享记录", "description": "您还没有创建任何文件分享", "action": "去创建分享"}, "expiryTime": {"permanent": "永久有效", "expired": "已过期", "expiresIn": "{time}后过期"}}, "detail": {"title": "分享详情", "basicInfo": {"title": "基本信息", "fileName": "文件名称", "fileSize": "文件大小", "shareCode": "分享码", "shareLink": "分享链接", "password": "访问密码", "noPassword": "无密码", "expiryTime": "过期时间", "createdTime": "创建时间", "status": "状态"}, "accessStats": {"title": "访问统计", "totalAccess": "总访问次数", "todayAccess": "今日访问", "lastAccessTime": "最后访问时间", "neverAccessed": "从未访问"}, "accessLog": {"title": "访问记录", "columns": {"accessTime": "访问时间", "ipAddress": "IP地址", "userAgent": "用户代理", "location": "地理位置"}, "empty": "暂无访问记录"}, "actions": {"copyLink": "复制链接", "copyCode": "复制分享码", "generateQR": "生成二维码", "disable": "禁用分享", "enable": "启用分享", "delete": "删除分享", "edit": "编辑分享"}}, "messages": {"copySuccess": "复制成功", "copyFailed": "复制失败", "disableSuccess": "分享已禁用", "disableFailed": "禁用失败", "enableSuccess": "分享已启用", "enableFailed": "启用失败", "deleteSuccess": "分享已删除", "deleteFailed": "删除失败", "deleteConfirm": "确定要删除这个分享吗？", "deleteConfirmContent": "删除后将无法恢复，且分享链接将失效", "loadFailed": "加载分享数据失败", "operationFailed": "操作失败，请稍后重试"}}, "profile": {"header": {"title": "个人资料", "subtitle": "管理您的个人信息和账户设置"}, "card": {"changeAvatar": "更换头像", "roleAdmin": "管理员", "roleUser": "普通用户", "statusActive": "正常", "statusDisabled": "禁用", "storageUsed": "已用空间", "storageQuota": "总空间", "usage": "使用率"}, "tabs": {"basic": "基本信息", "security": "安全设置", "storage": "存储管理", "preferences": "偏好设置"}, "avatarDialog": {"title": "更换头像", "drag_text_1": "点击或拖拽图片到此处", "drag_text_2": "支持 JPG、PNG、GIF 格式，最大 2MB", "preview": "头像预览", "cancel": "取消", "confirm": "确认上传", "error_format": "只能上传图片文件!", "error_size": "图片大小不能超过 2MB!", "error_select": "请先选择头像图片", "success": "头像更新成功", "fail": "头像上传失败"}, "basicInfo": {"title": "基本信息", "subtitle": "管理您的基本个人信息", "username": "用户名", "username_tip_1": "用户名不可修改", "username_tip_2": "用户名是您的唯一标识，注册后不可修改", "realName": "真实姓名", "realName_placeholder": "请输入真实姓名", "email": "邮箱地址", "email_placeholder": "请输入邮箱地址", "email_tip": "修改邮箱需要验证新邮箱地址", "phone": "联系电话", "phone_placeholder": "请输入联系电话", "bio": "个人简介", "bio_placeholder": "请输入个人简介（选填）", "registeredAt": "注册时间", "updatedAt": "最后更新", "reset": "重置", "save": "保存修改", "rules": {"realName_max": "真实姓名长度不能超过50个字符", "email_required": "请输入邮箱地址", "email_format": "请输入正确的邮箱格式", "phone_format": "请输入正确的手机号码", "bio_max": "个人简介不能超过200个字符"}, "messages": {"reset_confirm_title": "确认重置", "reset_confirm_text": "确定要重置所有修改吗？未保存的修改将会丢失。", "reset_success": "表单已重置", "no_change": "没有需要保存的修改", "update_success": "个人信息更新成功", "update_fail": "个人信息更新失败"}, "unknown": "未知", "format_error": "格式错误"}, "security": {"title": "安全设置", "subtitle": "管理您的账户安全和密码设置", "changePassword": "修改密码", "currentPassword": "当前密码", "currentPassword_placeholder": "请输入当前密码", "newPassword": "新密码", "newPassword_placeholder": "请输入新密码", "passwordStrength": "密码强度：", "strength": {"weak": "弱", "medium": "中", "strong": "强", "very_strong": "很强"}, "confirmPassword": "确认新密码", "confirmPassword_placeholder": "请再次输入新密码", "changePassword_button": "修改密码", "info": "安全信息", "lastLoginTime": "最后登录时间：", "lastLoginIP": "最后登录IP：", "passwordLastModified": "密码最后修改：", "daysAgo": "{days}天前", "suggestions": "安全建议", "suggestion_title": "为了您的账户安全，建议定期修改密码", "suggestion_1": "密码长度至少8位，包含大小写字母、数字和特殊字符", "suggestion_2": "不要使用与个人信息相关的密码", "suggestion_3": "建议每3个月更换一次密码", "suggestion_4": "不要在多个网站使用相同密码", "rules": {"current_required": "请输入当前密码", "new_required": "请输入新密码", "new_min": "密码长度至少8位", "new_pattern": "密码必须包含大小写字母、数字和特殊字符", "confirm_required": "请确认新密码", "confirm_match": "两次输入的密码不一致"}, "messages": {"success": "密码修改成功", "fail": "密码修改失败"}}, "storage": {"title": "存储管理", "subtitle": "查看和管理您的存储空间使用情况", "overview": "存储空间使用情况", "used_percentage": "已使用 {percentage}%", "fileTypeDistribution": "文件类型分布", "file_unit": "个文件", "file_types": {"document": "文档", "image": "图片", "video": "视频", "audio": "音频", "archive": "压缩包"}, "largestFiles": "占用空间最大的文件", "table": {"name": "文件名", "size": "文件大小", "type": "文件类型", "modified": "修改时间", "actions": "操作"}, "delete": "删除", "cleanup": "清理建议", "suggestions": {"duplicates_title": "清理重复文件", "duplicates_text": "检测到 {count} 个重复文件，可释放 {size} 空间", "start_cleanup": "开始清理", "trash_title": "清空回收站", "trash_text": "回收站中有 {count} 个文件，可释放 {size} 空间", "empty_trash": "清空回收站", "temp_title": "清理临时文件", "temp_text": "清理上传过程中产生的临时文件和缓存", "clear_cache": "清理缓存"}, "delete_confirm": {"title": "确认删除", "text": "确定要删除文件 \"{name}\" 吗？此操作不可恢复。", "success": "文件删除成功"}}, "preferences": {"title": "偏好设置", "subtitle": "个性化您的使用体验和界面偏好", "interface": "界面偏好", "themeMode": "主题模式", "themeMode_desc": "选择您喜欢的界面主题", "modes": {"light": "日间模式", "dark": "夜间模式", "auto": "跟随系统"}, "themeColor": "主题色", "themeColor_desc": "选择您喜欢的主题颜色", "language": "界面语言", "language_desc": "选择界面显示语言", "languages": {"zh_CN": "简体中文", "en_US": "English", "zh_TW": "繁體中文"}, "functional": "功能偏好", "defaultView": "默认视图模式", "defaultView_desc": "文件列表的默认显示方式", "views": {"grid": "网格视图", "list": "列表视图"}, "autoSave": "自动保存", "autoSave_desc": "自动保存用户偏好设置", "showHidden": "显示隐藏文件", "showHidden_desc": "在文件列表中显示隐藏文件", "notifications": "通知设置", "email_title": "邮件通知", "email_desc": "接收重要操作的邮件通知", "browser_title": "浏览器通知", "browser_desc": "接收浏览器推送通知", "share_title": "文件分享通知", "share_desc": "有人分享文件给您时通知", "storage_title": "存储空间警告", "storage_desc": "存储空间不足时提醒", "actions": {"reset": "恢复默认", "save": "保存设置"}, "messages": {"save_success": "偏好设置保存成功", "save_fail": "偏好设置保存失败", "reset_confirm_title": "确认重置", "reset_confirm_text": "确定要恢复所有设置为默认值吗？", "reset_success": "已恢复默认设置"}}}}, "transfer": {"title": "传输列表", "header": {"refresh": "刷新", "clearHistory": "清空历史"}, "stats": {"inProgress": "传输中", "completed": "已完成", "failed": "失败", "total": "总计"}, "connection": {"connecting_title": "正在连接实时服务", "connecting_desc": "正在建立实时连接，请稍候...", "reconnecting_title": "正在重新连接", "reconnecting_desc": "连接中断，正在尝试重连（第 {attempts} 次）...", "disconnected_title": "实时连接失败", "disconnected_desc": "多次重连失败，传输进度将无法实时更新，请检查网络连接。", "reconnect": "重新连接", "connected_title": "实时连接正常"}, "current": {"title": "当前传输", "empty": "暂无进行中的传输任务", "pause": "暂停", "resume": "继续", "cancel": "取消", "retry": "重试", "calculating": "计算中...", "uploading": "传输中"}, "upload": {"title": "上传任务"}, "download": {"title": "下载任务"}, "history": {"title": "传输历史", "empty": "暂无传输历史记录", "filter_all": "全部", "filter_completed": "已完成", "filter_failed": "失败", "table": {"fileName": "文件名", "size": "大小", "status": "状态", "completedAt": "完成时间", "actions": "操作"}, "delete": "删除", "retry": "重试"}, "status": {"PENDING": "等待中", "IN_PROGRESS": "传输中", "COMPLETED": "已完成", "FAILED": "失败", "CANCELLED": "已取消", "PAUSED": "已暂停"}, "transferDialogs": {"clear_title": "确认操作", "clear_text": "确定要清空所有传输历史记录吗？此操作不可恢复。", "cancel_title": "确认取消", "cancel_text": "确定要取消 {name} 的传输吗？", "delete_title": "确认删除", "delete_text": "确定要从历史记录中删除 {name} 吗？"}, "messages": {"refresh_ok": "传输列表已刷新", "history_cleared": "传输历史已清空", "cancel_wip": "取消传输功能开发中...", "pause_wip": "暂停功能开发中...", "resume_wip": "继续功能开发中...", "delete_ok": "历史记录已删除", "retry_wip": "重试功能开发中..."}, "socket": {"title": "传输列表"}}, "trash": {"title": "回收站", "stats": {"totalFiles": "个文件", "totalSize": "总大小", "expiringSoon": "个即将过期", "autoCleanup": "30天后自动清理"}, "toolbar": {"selectAll": "全选", "batchRestore": "批量还原", "batchDelete": "批量删除", "emptyTrash": "清空回收站", "refresh": "刷新", "sortBy": "排序方式"}, "sort": {"deletedAt_desc": "删除时间↓", "deletedAt_asc": "删除时间↑", "originalName_desc": "文件名↓", "originalName_asc": "文件名↑", "fileSize_desc": "文件大小↓", "fileSize_asc": "文件大小↑"}, "fileList": {"fileName": "文件名", "deletedTime": "删除时间", "size": "大小", "actions": "操作", "restore": "还原", "permanentDelete": "彻底删除", "deletedAt": "删除于", "folder": "文件夹", "expiringSoon": "即将过期"}, "dialogs": {"restoreTitle": "还原文件", "restoreFilesCount": "要还原的文件 ({count})", "restoreLocation": "还原位置", "restoreToOriginal": "还原到原位置", "restoreToRoot": "还原到根目录", "restoreToOriginalDesc": "文件将还原到删除前的文件夹中，如果原文件夹不存在则还原到根目录", "restoreToRootDesc": "所有文件都将还原到根目录中", "restoreConfirm": "确定还原", "deleteTitle": "彻底删除文件", "deleteWarning": "警告：此操作不可恢复", "deleteDescription": "您即将彻底删除以下文件，此操作将：", "deletePoint1": "从服务器永久删除文件数据", "deletePoint2": "无法通过任何方式恢复", "deletePoint3": "释放对应的存储空间", "deleteConfirmText": "请确认您真的要执行此操作！", "deleteConfirm": "彻底删除", "deleteFilesCount": "要彻底删除的文件 ({count})", "fileCount": "文件数量：", "totalSize": "总大小：", "expiringSoonLabel": "即将过期：", "expiringSoonFiles": "{count} 个文件", "confirmDeleteText": "请输入", "confirmDeleteKeyword": "确认删除", "confirmDeletePlaceholder": "请输入：确认删除", "confirmDeletePrompt": "来确认此操作：", "emptyTrashTitle": "清空回收站", "emptyTrashPrompt": "确定要清空回收站吗？这将彻底删除所有 {count} 个文件，此操作不可恢复。", "emptyTrashConfirm": "确定清空", "conflictInfo": "文件名冲突处理", "conflictDesc": "如果还原位置已存在同名文件，系统将自动重命名（如：文件名(1).txt）", "cancel": "取消"}, "messages": {"emptyState": "回收站为空", "goUpload": "去上传文件", "restoreSuccess": "成功还原 {count} 个文件", "restoreFailed": "还原文件失败", "deleteSuccess": "成功删除 {count} 个文件", "deleteFailed": "删除文件失败", "emptyTrashSuccess": "回收站已清空", "emptyTrashFailed": "清空回收站失败", "selectFiles": "请先选择要操作的文件", "loadFailed": "加载回收站文件失败", "refreshed": "回收站已刷新", "selectedFiles": "已选择 {selected} / {total} 个文件", "selectedSize": "，共 {size}"}, "time": {"expiryTime": "还有 {days} 天过期", "expired": "已过期", "justNow": "刚刚", "minutesAgo": "{minutes} 分钟前", "hoursAgo": "{hours} 小时前", "daysAgo": "{days} 天前"}}, "favorites": {"title": "收藏夹", "sidebar": {"favoriteFiles": "收藏文件", "allFavorites": "全部收藏", "recentFavorites": "最近收藏", "documentType": "文档类型", "imageType": "图片类型", "videoType": "视频类型", "otherType": "其他类型", "myNotes": "我的便签", "allNotes": "全部便签", "recentNotes": "最近编辑", "favoriteNotes": "收藏便签", "reminderNotes": "待办事项", "tagManagement": "标签管理", "fileTags": "文件标签", "noteTags": "便签标签", "customGroups": "自定义分组"}, "toolbar": {"searchPlaceholder": "搜索收藏文件或便签...", "selectCategory": "选择分类", "allCategories": "全部分类", "listView": "列表", "gridView": "网格", "refresh": "刷新", "newNote": "新建便签", "more": "更多", "import": "导入便签", "export": "导出便签", "batchDelete": "批量删除", "settings": "设置"}, "categories": {"default": "默认", "work": "工作", "study": "学习", "life": "生活"}, "actions": {"preview": "预览", "download": "下载", "edit": "编辑", "share": "分享", "remove": "取消收藏", "delete": "删除", "copy": "复制", "move": "移动", "export": "导出", "favorite": "收藏", "unfavorite": "取消收藏"}, "dialogs": {"close": "关闭", "cancel": "取消", "confirm": "确定"}, "messages": {"removeSuccess": "取消收藏成功", "removeFailed": "取消收藏失败", "updateSuccess": "更新成功", "updateFailed": "更新失败", "deleteSuccess": "删除成功", "deleteFailed": "删除失败", "loadFailed": "加载数据失败", "loadFilesFailed": "加载收藏文件失败", "loadNotesFailed": "加载便签失败", "createSuccess": "创建成功", "createFailed": "创建失败", "saveSuccess": "保存成功", "saveFailed": "保存失败", "operationSuccess": "操作成功", "operationFailed": "操作失败", "importInDevelopment": "导入功能开发中...", "exportInDevelopment": "导出功能开发中...", "batchDeleteInDevelopment": "批量删除功能开发中...", "settingsInDevelopment": "设置功能开发中..."}, "folders": {"defaultGroup": "默认分组"}, "notes": {"emptyState": "暂无便签", "createFirst": "创建第一个便签", "title": "标题", "folder": "文件夹", "tags": "标签", "updateTime": "更新时间", "viewCount": "查看次数", "actions": "操作", "noContent": "暂无内容", "noTags": "无标签", "edit": "编辑", "copy": "复制", "export": "导出", "delete": "删除", "more": "更多", "preview": "预览", "favorite": "收藏", "unfavorite": "取消收藏", "detail": "便签详情", "createdAt": "创建时间：", "updatedAt": "更新时间：", "folderLabel": "文件夹：", "editNote": "编辑便签", "toggleFavorite": "切换收藏", "exportMarkdown": "导出Markdown", "renderedView": "渲染视图", "sourceView": "源码视图", "exportSuccess": "导出成功", "exportFailed": "导出失败", "unknownError": "未知错误", "reminderTime": "提醒时间：", "editTitle": "编辑便签", "createTitle": "新建便签", "titlePlaceholder": "请输入便签标题", "selectFolder": "选择文件夹", "defaultFolder": "默认分组", "favorited": "已收藏", "addToFavorite": "收藏", "fullscreen": "全屏", "exitFullscreen": "退出全屏", "fullscreenEdit": "全屏编辑", "exitFullscreenEdit": "退出全屏", "addTag": "添加标签", "setReminder": "设置提醒", "save": "保存", "saveAndContinue": "保存并继续", "contentPlaceholder": "开始编写你的便签内容...", "markdownPlaceholder": "支持Markdown语法，开始编写你的便签...", "hasUnsavedChanges": "有未保存的更改，确定要关闭吗？", "confirmClose": "确认关闭", "shortcuts": "快捷键：", "shortcutSave": "Ctrl+S 保存", "shortcutSaveClose": "Ctrl+Enter 保存并关闭", "shortcutClose": "Esc 关闭", "selectTags": "选择标签", "createTag": "新建标签", "reminderLabel": "提醒时间", "selectReminderTime": "选择提醒时间"}, "tags": {"fileTab": "文件标签", "noteTab": "便签标签", "createFileTag": "新建文件标签", "createNoteTag": "新建便签标签", "totalTags": "标签总数", "totalUsage": "总使用次数", "noFileTags": "暂无文件标签", "noNoteTags": "暂无便签标签", "createFirstFileTag": "创建第一个文件标签", "createFirstNoteTag": "创建第一个便签标签", "usageCount": "使用次数", "editTag": "编辑标签", "deleteTag": "删除标签", "tagName": "标签名称", "tagColor": "标签颜色", "tagNamePlaceholder": "请输入标签名称", "createTagTitle": "创建标签", "editTagTitle": "编辑标签", "deleteConfirm": "确定要删除这个标签吗？", "deleteWarning": "删除后无法恢复"}, "files": {"emptyState": "暂无收藏文件", "goToFiles": "去文件管理收藏文件", "fileName": "文件名", "category": "分类", "notes": "备注", "favoriteTime": "收藏时间", "fileSize": "文件大小", "actions": "操作", "preview": "预览", "download": "下载", "share": "分享", "editNotes": "编辑备注", "removeFavorite": "取消收藏", "batchRemove": "批量取消收藏", "selectAll": "全选", "selectedCount": "已选择 {count} 项"}, "detail": {"title": "文件详情", "basicInfo": "基本信息", "favoriteInfo": "收藏信息", "fileSize": "文件大小:", "fileType": "文件类型:", "uploadTime": "上传时间:", "modifyTime": "修改时间:", "favoriteTime": "收藏时间:", "category": "分类:", "notes": "备注:", "noNotes": "暂无备注", "actions": "操作", "preview": "预览", "download": "下载", "share": "分享", "editNotes": "编辑备注", "removeFavorite": "取消收藏"}}, "aiChat": {"title": "AI助手", "subtitle": "智能对话助手，为您提供专业解答", "clearChat": "清空对话", "settings": "设置", "thinking": "AI正在思考...", "inputPlaceholder": "输入您的问题...", "noApiKeyPlaceholder": "请先在设置中配置API Key", "noApiKeyTitle": "未配置API Key", "noApiKeyDesc": "请前往设置页面配置您的OpenRouter API Key以使用AI助手功能", "goToSettings": "前往设置", "sendMessage": "发送", "retry": "重试", "copy": "复制", "copied": "已复制", "user": "我", "assistant": "AI助手", "error": "出错了", "networkError": "网络连接失败，请检查网络后重试", "apiError": "API调用失败，请检查API Key配置", "unknownError": "未知错误，请稍后重试", "welcomeMessage": "您好！我是您的AI助手，有什么可以帮助您的吗？", "modelSelector": {"title": "选择模型", "current": "当前模型"}, "feedbackLike": "感谢您的反馈！", "feedbackDislike": "感谢您的反馈，我们会持续改进！", "roleSelector": {"selectRole": "选择AI角色", "customRole": "自定义角色", "manageRoles": "管理角色", "exportRoles": "导出角色", "importRoles": "导入角色", "switchedTo": "已切换到{name}"}, "roleCustom": {"title": "自定义AI角色", "name": "角色名称", "description": "角色描述", "category": "角色分类", "avatar": "角色头像", "prompt": "系统提示", "preview": "角色预览", "create": "创建角色", "cancel": "取消", "nameRequired": "请输入角色名称", "descRequired": "请输入角色描述", "promptRequired": "请输入系统提示词", "createSuccess": "角色创建成功"}}, "team": {"management": "团队管理", "managementDesc": "创建和管理您的团队，邀请成员协作", "createTeam": "创建团队", "createFirstTeam": "创建第一个团队", "allTeams": "全部团队", "approvedTeams": "已批准团队", "pendingTeams": "待审核", "noTeams": "暂无团队", "admin": "管理员", "chatRoom": "聊天室", "chat": "团队聊天", "owner": "所有者", "searchPlaceholder": "搜索团队名称...", "enter": "进入", "overview": "团队总览", "teamStatus": "团队状态", "teamName": "团队名称", "teamNamePlaceholder": "请输入团队名称", "teamNameRequired": "团队名称不能为空", "teamNameLength": "团队名称长度必须在2-50个字符之间", "teamDescription": "团队描述", "teamDescriptionPlaceholder": "请输入团队描述（可选）", "teamDescriptionLength": "团队描述不能超过500个字符", "noDescription": "暂无描述", "maxMembers": "最大成员数", "maxMembersRequired": "最大成员数不能为空", "maxMembersRange": "最大成员数必须在2-200之间", "teamAvatar": "团队头像", "teamAvatarPlaceholder": "请输入头像URL（可选）", "teamAvatarInvalid": "请输入有效的URL地址", "avatarPreview": "头像预览", "members": "成员", "created": "创建时间", "memberProgress": "成员进度：{current}/{max}", "vipRequired": "VIP权限要求", "vipRequiredDesc": "创建团队需要VIP或管理员权限，升级VIP可享受以下特权：", "vipBenefit1": "创建无限数量的团队", "vipBenefit2": "享受更大的存储空间", "vipBenefit3": "使用高级协作功能", "approvalRequired": "审核提醒", "approvalRequiredDesc": "团队创建后需要管理员审核，审核通过后即可正常使用", "createSuccess": "团队创建成功", "updateSuccess": "团队更新成功", "joinSuccess": "加入团队成功", "editTeam": "编辑团队", "teamInfo": "团队信息", "teamId": "团队ID", "status": "状态", "currentMembers": "当前成员", "maxMembersTip": "不能少于当前成员数量（{current}）", "maxMembersEditRange": "最大成员数不能少于当前成员数量（{min}）", "joinTeam": "加入团队", "joinByInviteCode": "通过邀请码加入", "joinBySearch": "搜索团队加入", "inviteCode": "邀请码", "inviteCodePlaceholder": "请输入8位邀请码", "inviteCodeRequired": "邀请码不能为空", "inviteCodeLength": "邀请码长度必须在6-16个字符之间", "inviteCodeTip": "邀请码说明", "inviteRole": "邀请成员", "inviteCodeTipDesc": "请向团队管理员获取邀请码，输入后即可加入对应团队", "searchJoinComingSoon": "搜索加入功能即将上线", "searchJoinComingSoonDesc": "该功能正在开发中，敬请期待", "recentInviteCodes": "最近使用的邀请码", "useThisCode": "使用", "maxUses": "最大使用次数", "back": "返回", "more": "更多", "copy": "复制", "teamMembers": "团队成员", "inviteMembers": "邀请成员", "teamSettings": "团队设置", "deleteSuccess": "删除成功", "fetchDetailError": "获取团队详情失败", "teamStats": "团队统计", "totalMembers": "总成员数", "createdDate": "创建日期", "memberUsage": "成员使用率", "noMembers": "暂无成员", "joinedAt": "加入时间", "promoteToAdmin": "提升为管理员", "demoteToMember": "降级为成员", "transferOwnership": "转移所有权", "removeMember": "移除成员", "promoteSuccess": "提升成功", "demoteSuccess": "降级成功", "transferSuccess": "所有权转移成功", "transferOwnershipConfirm": "确定要转移所有权吗？", "inviteCodes": "邀请码", "generateCode": "生成邀请码", "noInviteCodes": "暂无邀请码", "generateFirstCode": "生成第一个邀请码", "refreshCode": "刷新邀请码", "disableCode": "禁用邀请码", "copySuccess": "复制成功", "generateSuccess": "生成成功", "usageCount": "使用次数", "expireTime": "过期时间", "expiresAt": "过期于", "generateInviteCode": "生成邀请码", "inviteRoleRequired": "邀请角色不能为空", "expireTimeInvalid": "过期时间必须是未来时间", "maxUsesRange": "最大使用次数必须在1-100之间", "memberRole": "普通成员", "adminRole": "管理员", "inviteRoleTip": "被邀请用户加入团队后的初始角色", "expireTimePlaceholder": "选择过期时间（可选）", "expireTimeTip": "不设置则永不过期", "maxUsesPlaceholder": "输入最大使用次数", "maxUsesTip": "不设置则无限制使用", "quickSettings": "快速设置", "oneHour": "1小时", "oneDay": "1天", "oneWeek": "1周", "unlimited": "无限制", "codePreview": "邀请码预览", "neverExpire": "永不过期", "inviteByUserId": "通过用户ID邀请", "inviteByCode": "通过邀请码邀请", "userId": "用户ID", "userIdPlaceholder": "请输入要邀请的用户ID", "userIdRequired": "用户ID不能为空", "userIdInvalid": "请输入有效的用户ID", "roleRequired": "角色不能为空", "directInviteTip": "直接邀请说明", "directInviteTipDesc": "直接通过用户ID邀请，用户将立即成为团队成员", "activeInviteCodes": "活跃的邀请码", "noActiveInviteCodes": "暂无活跃的邀请码", "codeInviteTip": "邀请码说明", "codeInviteTipDesc": "将邀请码分享给其他用户，他们可以通过邀请码主动加入团队", "inviteMember": "邀请成员", "generateNewCode": "生成新邀请码", "inviteSuccess": "邀请成功", "pendingApprovalTitle": "团队待审核", "pendingApprovalDesc": "您的团队正在等待管理员审核，审核通过后即可使用完整功能。", "teamApproval": "团队审核", "teamApprovalDesc": "管理和审核用户创建的团队申请", "approveTeam": "批准团队", "rejectTeam": "拒绝团队", "approve": "批准", "reject": "拒绝", "approveSuccess": "团队审核通过", "rejectSuccess": "团队已拒绝", "rejectedTeams": "已拒绝团队", "pendingTeamsList": "待审核团队列表", "noPendingTeams": "暂无待审核的团队", "refreshError": "刷新失败", "approveConfirm": "确定要批准团队 \"{teamName}\" 吗？", "rejectConfirm": "确定要拒绝团队 \"{teamName}\" 吗？", "teamDetail": "团队详情", "basicInfo": "基本信息", "creator": "创建者", "createdAt": "创建时间", "removeMemberSuccess": "成员移除成功", "changeRole": "变更角色", "currentRole": "当前角色", "selectNewRole": "选择新角色", "rolePermissions": "角色权限", "roleChangeSuccess": "角色变更成功"}, "project": {"kanban": "项目看板", "taskManagement": "任务管理", "announcements": "公告通知", "projects": "个项目", "createProject": "创建项目", "editProject": "编辑项目", "deleteProject": "删除项目", "name": "项目名称", "namePlaceholder": "请输入项目名称", "nameRequired": "请输入项目名称", "nameLength": "项目名称长度应在2-100个字符之间", "description": "项目描述", "descriptionPlaceholder": "请输入项目描述", "descriptionLength": "项目描述不能超过500个字符", "priority": {"description": "优先级", "low": "低", "medium": "中", "high": "高", "urgent": "紧急"}, "priorityPlaceholder": "请选择优先级", "priorityRequired": "请选择优先级", "startDate": "开始日期", "startDatePlaceholder": "请选择开始日期", "endDate": "结束日期", "endDatePlaceholder": "请选择结束日期", "endDateError": "结束日期不能早于开始日期", "manager": "项目经理", "managerPlaceholder": "请选择项目经理", "progress": "进度", "status": {"planning": "规划中", "active": "进行中", "archived": "已归档", "todo": "待办", "inProgress": "进行中", "review": "审核中", "completed": "已完成", "cancelled": "已取消"}, "noPlanningProjects": "暂无规划中的项目", "noActiveProjects": "暂无进行中的项目", "noCompletedProjects": "暂无已完成的项目", "noArchivedProjects": "暂无已归档的项目", "noProjects": "暂无项目", "fetchError": "获取项目列表失败", "statusUpdateSuccess": "项目状态更新成功", "statusUpdateError": "项目状态更新失败", "createSuccess": "创建项目成功", "createError": "创建项目失败", "updateSuccess": "更新项目成功", "updateError": "更新项目失败", "deleteConfirmTitle": "删除项目", "deleteConfirmMessage": "确定要删除项目 {name} 吗？此操作不可恢复。", "deleteSuccess": "删除项目成功", "deleteError": "删除项目失败", "overdue": "已逾期", "nearDeadline": "即将到期", "tasks": "任务", "createTask": "创建任务", "editTask": "编辑任务", "taskTitle": "任务标题", "taskTitlePlaceholder": "请输入任务标题", "taskTitleRequired": "请输入任务标题", "taskTitleLength": "任务标题长度应在2-200个字符之间", "taskDescription": "任务描述", "taskDescriptionPlaceholder": "请输入任务描述", "taskDescriptionLength": "任务描述不能超过1000个字符", "assignee": "负责人", "assigneePlaceholder": "请选择负责人", "unassigned": "未分配", "dueDate": "截止日期", "dueDatePlaceholder": "请选择截止日期", "estimatedHours": "预估工时", "estimatedHoursPlaceholder": "请输入预估工时", "estimatedHoursRange": "预估工时应在0-999小时之间", "actualHours": "实际工时", "actualHoursPlaceholder": "请输入实际工时", "actualHoursRange": "实际工时应在0-999小时之间", "taskStatus": "任务状态", "completedAt": "完成时间", "taskProgress": "任务进度", "completed": "已完成", "hours": "工时", "estimated": "预估", "actual": "实际", "taskTags": "任务标签", "taskTagsPlaceholder": "请输入或选择标签", "taskDependencies": "任务依赖", "taskDependenciesPlaceholder": "请选择依赖的任务", "tags": {"frontend": "前端", "backend": "后端", "database": "数据库", "ui": "UI", "api": "API", "testing": "测试", "documentation": "文档", "bugfix": "Bug修复", "feature": "新功能", "optimization": "优化"}, "selectProject": "选择项目", "allStatus": "所有状态", "allAssignees": "所有负责人", "searchTasks": "搜索任务", "totalTasks": "总任务数", "todoTasks": "待办任务", "inProgressTasks": "进行中任务", "completedTasks": "已完成任务", "selectProjectFirst": "请先选择项目", "noTasks": "暂无任务", "createFirstTask": "创建第一个任务", "fetchTasksError": "获取任务列表失败", "createTaskSuccess": "创建任务成功", "createTaskError": "创建任务失败", "updateTaskSuccess": "更新任务成功", "updateTaskError": "更新任务失败", "deleteTaskConfirmTitle": "删除任务", "deleteTaskConfirmMessage": "确定要删除任务 {title} 吗？此操作不可恢复。", "deleteTaskSuccess": "删除任务成功", "deleteTaskError": "删除任务失败", "taskStatusUpdateSuccess": "任务状态更新成功", "taskStatusUpdateError": "任务状态更新失败", "taskAssignSuccess": "任务分配成功", "taskAssignError": "任务分配失败", "createAnnouncement": "创建公告", "editAnnouncement": "编辑公告", "announcementTitle": "公告标题", "announcementTitlePlaceholder": "请输入公告标题", "announcementTitleRequired": "请输入公告标题", "announcementTitleLength": "公告标题长度应在2-200个字符之间", "announcementContent": "公告内容", "announcementContentPlaceholder": "请输入公告内容", "announcementContentRequired": "请输入公告内容", "announcementContentMinLength": "公告内容不能少于10个字符", "pinAnnouncement": "置顶公告", "unpinAnnouncement": "取消置顶", "pinAnnouncementHint": "置顶的公告将显示在公告列表顶部", "write": "编辑", "preview": "预览", "markdownSupported": "支持Markdown格式，", "markdownGuide": "查看Markdown指南", "noContentToPreview": "暂无内容可预览", "publish": "发布", "pinned": "置顶", "pinnedAnnouncements": "置顶公告", "regularAnnouncements": "普通公告", "unknownUser": "未知用户", "showMore": "显示更多", "showLess": "收起", "allTime": "所有时间", "today": "今日", "thisWeek": "本周", "thisMonth": "本月", "searchAnnouncements": "搜索公告", "totalAnnouncements": "总公告数", "todayAnnouncements": "今日公告", "weekAnnouncements": "本周公告", "monthAnnouncements": "本月公告", "noAnnouncements": "暂无公告", "createFirstAnnouncement": "创建第一条公告", "fetchAnnouncementsError": "获取公告列表失败", "createAnnouncementSuccess": "创建公告成功", "createAnnouncementError": "创建公告失败", "updateAnnouncementSuccess": "更新公告成功", "updateAnnouncementError": "更新公告失败", "deleteAnnouncementConfirmTitle": "删除公告", "deleteAnnouncementConfirmMessage": "确定要删除公告 {title} 吗？此操作不可恢复。", "deleteAnnouncementSuccess": "删除公告成功", "deleteAnnouncementError": "删除公告失败", "pinAnnouncementSuccess": "置顶公告成功", "pinAnnouncementError": "置顶公告失败", "unpinAnnouncementSuccess": "取消置顶成功", "unpinAnnouncementError": "取消置顶失败"}}