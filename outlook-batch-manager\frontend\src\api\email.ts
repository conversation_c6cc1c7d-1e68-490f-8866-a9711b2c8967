import axios from 'axios'
import type { Email, SendEmailRequest } from '@/types/email'

const api = axios.create({
  baseURL: '/api',
  timeout: 30000
})

// 获取邮件列表
export const getEmails = async (params: {
  accountId?: string
  page?: number
  pageSize?: number
  folder?: string
  search?: string
}): Promise<{ emails: Email[], total: number }> => {
  const response = await api.get('/emails', { params })
  return response.data
}

// 获取邮件详情
export const getEmailDetail = async (emailId: string): Promise<Email> => {
  const response = await api.get(`/emails/${emailId}`)
  return response.data
}

// 标记邮件已读/未读
export const markEmailRead = async (emailIds: string[], isRead: boolean): Promise<void> => {
  await api.patch('/emails/read', { emailIds, isRead })
}

// 删除邮件
export const deleteEmails = async (emailIds: string[]): Promise<void> => {
  await api.delete('/emails', { data: { emailIds } })
}

// 发送邮件
export const sendEmail = async (emailData: SendEmailRequest): Promise<void> => {
  const formData = new FormData()
  
  // 添加基本字段
  formData.append('accountEmail', emailData.accountEmail)
  formData.append('subject', emailData.subject)
  formData.append('body', emailData.body)
  formData.append('bodyType', emailData.bodyType)
  formData.append('toRecipients', JSON.stringify(emailData.toRecipients))
  
  if (emailData.ccRecipients) {
    formData.append('ccRecipients', JSON.stringify(emailData.ccRecipients))
  }
  
  if (emailData.bccRecipients) {
    formData.append('bccRecipients', JSON.stringify(emailData.bccRecipients))
  }
  
  if (emailData.importance) {
    formData.append('importance', emailData.importance)
  }
  
  // 添加附件
  if (emailData.attachments) {
    emailData.attachments.forEach((file, index) => {
      formData.append(`attachments`, file)
    })
  }
  
  await api.post('/emails/send', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 批量发送邮件
export const sendBatchEmails = async (emails: SendEmailRequest[]): Promise<void> => {
  await api.post('/emails/batch-send', { emails })
}
