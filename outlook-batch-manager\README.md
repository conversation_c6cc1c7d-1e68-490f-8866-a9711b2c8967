# Outlook邮箱批量管理工具

一个基于Vue3和Node.js的多账户Outlook邮箱批量管理工具，支持统一管理多个邮箱账户，批量处理邮件操作。

## ✨ 功能特性

- 🔐 **安全认证**: 使用Microsoft Graph API官方OAuth2认证
- 📧 **多账户管理**: 统一管理多个Outlook邮箱账户
- 📬 **统一收件箱**: 聚合显示所有账户的邮件
- ⚡ **批量操作**: 支持批量标记、删除、移动邮件
- 📝 **邮件发送**: 选择发件账户发送邮件
- 🔍 **全局搜索**: 跨账户搜索邮件内容
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 🛠 技术栈

### 前端
- Vue3 + TypeScript
- Element Plus UI组件库
- Pinia状态管理
- SCSS样式预处理
- Vite构建工具

### 后端
- Node.js + Express
- TypeScript
- SQLite数据库
- Microsoft Graph API

## 📁 项目结构

```
outlook-batch-manager/
├── doc/                          # 项目文档
├── frontend/                     # Vue3前端应用
├── backend/                      # Node.js后端服务
└── README.md                     # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 1. 克隆项目
```bash
git clone <repository-url>
cd outlook-batch-manager
```

### 2. 安装依赖

#### 安装前端依赖
```bash
cd frontend
npm install
```

#### 安装后端依赖
```bash
cd ../backend
npm install
```

### 3. 配置环境变量

在backend目录下创建`.env`文件：
```env
# Microsoft Graph API配置
MICROSOFT_CLIENT_ID=your_client_id
MICROSOFT_CLIENT_SECRET=your_client_secret
MICROSOFT_REDIRECT_URI=http://localhost:3000/auth/callback

# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DATABASE_PATH=./data/database.sqlite
```

### 4. Microsoft Azure应用注册

1. 访问 [Azure Portal](https://portal.azure.com)
2. 进入"应用注册"
3. 点击"新注册"
4. 填写应用信息：
   - 名称：Outlook Batch Manager
   - 支持的账户类型：任何组织目录中的账户和个人Microsoft账户
   - 重定向URI：Web - `http://localhost:3000/auth/callback`
5. 注册完成后，记录"应用程序(客户端)ID"
6. 在"证书和密码"中创建客户端密码
7. 在"API权限"中添加以下权限：
   - Microsoft Graph > 委托的权限 > Mail.Read
   - Microsoft Graph > 委托的权限 > Mail.Send
   - Microsoft Graph > 委托的权限 > Mail.ReadWrite
   - Microsoft Graph > 委托的权限 > User.Read

### 5. 启动应用

#### 启动后端服务
```bash
cd backend
npm run dev
```

#### 启动前端应用
```bash
cd frontend
npm run dev
```

### 6. 访问应用
打开浏览器访问：`http://localhost:5173`

## 📖 使用说明

### 添加邮箱账户
1. 点击"添加账户"按钮
2. 跳转到Microsoft登录页面
3. 输入邮箱账户和密码
4. 授权应用访问邮箱
5. 返回应用，账户添加成功

### 管理邮件
1. 在统一收件箱中查看所有账户的邮件
2. 使用搜索功能快速找到特定邮件
3. 选择多封邮件进行批量操作
4. 点击邮件查看详细内容

### 发送邮件
1. 点击"写邮件"按钮
2. 选择发件账户
3. 填写收件人、主题和内容
4. 添加附件（可选）
5. 点击发送

## 🔧 开发指南

### 前端开发
```bash
cd frontend
npm run dev      # 启动开发服务器
npm run build    # 构建生产版本
npm run preview  # 预览生产版本
```

### 后端开发
```bash
cd backend
npm run dev      # 启动开发服务器（热重载）
npm run build    # 编译TypeScript
npm start        # 启动生产服务器
```

## 📝 API文档

### 认证相关
- `GET /auth/login` - 获取Microsoft登录URL
- `GET /auth/callback` - OAuth2回调处理
- `POST /auth/refresh` - 刷新访问令牌

### 账户管理
- `GET /api/accounts` - 获取账户列表
- `DELETE /api/accounts/:id` - 删除账户

### 邮件操作
- `GET /api/emails` - 获取邮件列表
- `GET /api/emails/:id` - 获取邮件详情
- `POST /api/emails/send` - 发送邮件
- `PATCH /api/emails/batch` - 批量操作邮件

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Microsoft Graph API](https://docs.microsoft.com/en-us/graph/) - 提供邮箱访问接口
- [Vue.js](https://vuejs.org/) - 前端框架
- [Element Plus](https://element-plus.org/) - UI组件库
- [Express.js](https://expressjs.com/) - 后端框架

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看[项目文档](./doc/项目总体规划文档.md)
2. 搜索已有的[Issues](../../issues)
3. 创建新的Issue描述问题

---

⭐ 如果这个项目对您有帮助，请给我们一个Star！
