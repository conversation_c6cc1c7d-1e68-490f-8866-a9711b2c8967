<template>
  <div class="vip-center" :class="{ 'dark': isDark }">
    <div class="page-header">
      <h1 class="page-title">{{ t('dashboard.vipCenter.title') }}</h1>
      <p class="page-subtitle">{{ t('dashboard.vipCenter.subtitle') }}</p>
    </div>

    <div class="vip-content">
      <!-- VIP状态卡片 -->
      <div class="status-section">
        <VipStatusCard />
      </div>

      <!-- 功能权限展示 -->
      <div class="features-section">
        <el-card class="features-card">
          <template #header>
            <h3>{{ t('dashboard.vipCenter.features.title') }}</h3>
          </template>
          <div class="features-grid">
            <div 
              v-for="feature in vipFeatures" 
              :key="feature.code"
              class="feature-item"
              :class="{ 'available': isVip || feature.free }"
            >
              <div class="feature-icon">
                <el-icon :size="32" :color="getFeatureIconColor(feature, isVip)">
                  <component :is="feature.icon" />
                </el-icon>
              </div>
              <div class="feature-info">
                <h4 class="feature-title">{{ t(feature.titleKey) }}</h4>
                <p class="feature-description">{{ t(feature.descriptionKey) }}</p>
                <div class="feature-limit">
                  <span v-if="feature.free" class="free-badge">{{ t('dashboard.vipCenter.badges.free') }}</span>
                  <span v-else-if="isVip" class="vip-badge">{{ t('dashboard.vipCenter.badges.vipExclusive') }}</span>
                  <span v-else class="limit-text">{{ t(feature.basicLimitKey) }}</span>
                </div>
              </div>
              <div class="feature-status">
                <el-icon v-if="isVip || feature.free" class="status-icon available">
                  <Check />
                </el-icon>
                <el-icon v-else class="status-icon locked">
                  <Lock />
                </el-icon>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 订阅管理 -->
      <div class="subscription-section">
        <SubscriptionManagement />
      </div>
    </div>

    <!-- VIP升级对话框 -->
    <VipUpgradeDialog />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { 
  Check, 
  Lock,
  FolderOpened,
  User,
  Document,
  Share,
  Headset,
  Setting,
  Key
} from '@element-plus/icons-vue'
import { useVipStore } from '@/stores/vip'
import { useThemeStore } from '@/stores/theme'
import VipStatusCard from '@/components/vip/VipStatusCard.vue'
import VipUpgradeDialog from '@/components/vip/VipUpgradeDialog.vue'
import SubscriptionManagement from '@/components/vip/SubscriptionManagement.vue'

const { t } = useI18n()
const vipStore = useVipStore()
const themeStore = useThemeStore()

// 计算当前是否为暗色模式
const isDark = computed(() => themeStore.isDark)

const vipFeatures = [
  {
    code: 'STORAGE',
    icon: FolderOpened,
    titleKey: 'dashboard.vipCenter.features.storage.title',
    descriptionKey: 'dashboard.vipCenter.features.storage.description',
    basicLimitKey: 'dashboard.vipCenter.features.storage.basicLimit',
    vipLimitKey: 'dashboard.vipCenter.features.storage.vipLimit',
    free: false
  },
  {
    code: 'TEAM_CREATE',
    icon: User,
    titleKey: 'dashboard.vipCenter.features.teamCreate.title',
    descriptionKey: 'dashboard.vipCenter.features.teamCreate.description',
    basicLimitKey: 'dashboard.vipCenter.features.teamCreate.basicLimit',
    vipLimitKey: 'dashboard.vipCenter.features.teamCreate.vipLimit',
    free: false
  },
  {
    code: 'AI_REPORT',
    icon: Document,
    titleKey: 'dashboard.vipCenter.features.aiReport.title',
    descriptionKey: 'dashboard.vipCenter.features.aiReport.description',
    basicLimitKey: 'dashboard.vipCenter.features.aiReport.basicLimit',
    vipLimitKey: 'dashboard.vipCenter.features.aiReport.vipLimit',
    free: false
  },
  {
    code: 'PRIORITY_SUPPORT',
    icon: Headset,
    titleKey: 'dashboard.vipCenter.features.prioritySupport.title',
    descriptionKey: 'dashboard.vipCenter.features.prioritySupport.description',
    basicLimitKey: 'dashboard.vipCenter.features.prioritySupport.basicLimit',
    vipLimitKey: 'dashboard.vipCenter.features.prioritySupport.vipLimit',
    free: false
  }
]

// 计算属性
const isVip = computed(() => vipStore.isVip)

onMounted(async () => {
  await vipStore.init()
  // 主动刷新功能使用统计，使用前端现有数据
  await vipStore.enhanceFeatureUsagesWithFrontendData()
})

// 方法
const getFeatureIconColor = (feature: any, isVip: boolean) => {
  if (feature.free || isVip) {
    return '#10b981'
  }
  return isDark.value ? '#6b7280' : '#9ca3af'
}
</script>

<style scoped>
.vip-center {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  background: #fafbfc;
  transition: background-color 0.2s ease;
}

.vip-center.dark {
  background: #0f172a;
}

.page-header {
  text-align: center;
  margin-bottom: 8px;
  padding: 07px 16px;
  background: white;
  /* border-radius: 16px; */
  /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06); */
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.vip-center.dark .page-header {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

.page-title {
  font-size: 36px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 10px 0;
  transition: color 0.2s ease;
}

.vip-center.dark .page-title {
  color: #f1f5f9;
}

.page-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  transition: color 0.2s ease;
}

.vip-center.dark .page-subtitle {
  color: #94a3b8;
}

.vip-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.status-section {
  width: 100%;
}

.features-section {
  width: 100%;
}

.features-card {
  background: white;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.vip-center.dark .features-card {
  background: #1e293b;
  border: 1px solid #334155;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

.features-card :deep(.el-card__header) {
  background: #f8fafc;
  color: #1f2937;
  /* border-radius: 16px 16px 0 0; */
  border-bottom: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.vip-center.dark .features-card :deep(.el-card__header) {
  background: #0f172a;
  color: #f1f5f9;
  border-bottom: 1px solid #334155;
}

.features-card :deep(.el-card__header h3) {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  padding: 10px 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  transition: all 0.2s ease;
}

.vip-center.dark .feature-item {
  border: 1px solid #334155;
  background: #0f172a;
}

.feature-item.available {
  border-color: #10b981;
  background: #ecfdf5;
}

.vip-center.dark .feature-item.available {
  border-color: #10b981;
  background: #064e3b;
}

.feature-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.vip-center.dark .feature-item:hover {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f1f5f9;
  transition: background-color 0.2s ease;
}

.vip-center.dark .feature-icon {
  background: #1e293b;
}

.feature-info {
  flex: 1;
}

.feature-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 5px 0;
  transition: color 0.2s ease;
}

.vip-center.dark .feature-title {
  color: #f1f5f9;
}

.feature-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.4;
  transition: color 0.2s ease;
}

.vip-center.dark .feature-description {
  color: #94a3b8;
}

.feature-limit {
  font-size: 12px;
}

.free-badge {
  background: #10b981;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.vip-badge {
  background: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.limit-text {
  color: #9ca3af;
  transition: color 0.2s ease;
}

.vip-center.dark .limit-text {
  color: #6b7280;
}

.feature-status {
  flex-shrink: 0;
}

.status-icon {
  font-size: 24px;
}

.status-icon.available {
  color: #10b981;
}

.status-icon.locked {
  color: #9ca3af;
  transition: color 0.2s ease;
}

.vip-center.dark .status-icon.locked {
  color: #6b7280;
}

.subscription-section {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vip-center {
    padding: 10px;
  }
  
  .page-header {
    padding: 30px 15px;
  }
  
  .page-title {
    font-size: 28px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .feature-info {
    text-align: center;
  }
}
</style>