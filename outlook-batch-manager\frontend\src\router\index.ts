import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/views/Layout.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表板' }
      },
      {
        path: '/accounts',
        name: 'Accounts',
        component: () => import('@/views/Accounts.vue'),
        meta: { title: '账户管理' }
      },
      {
        path: '/emails',
        name: 'Emails',
        component: () => import('@/views/Emails.vue'),
        meta: { title: '邮件管理' }
      },
      {
        path: '/compose',
        name: 'Compose',
        component: () => import('@/views/Compose.vue'),
        meta: { title: '写邮件' }
      }
    ]
  },
  {
    path: '/auth/callback',
    name: 'AuthCallback',
    component: () => import('@/views/AuthCallback.vue'),
    meta: { title: '授权回调' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
