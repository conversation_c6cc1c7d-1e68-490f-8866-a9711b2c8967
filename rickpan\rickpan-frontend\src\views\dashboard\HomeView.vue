<template>
  <div class="home-view">
    <!-- Canvas背景动效 -->
    <BackgroundCanvas />
    <!-- 欢迎区域 -->
    <div class="welcome-section" :class="userTypeClass">
      <div class="welcome-content">
        <div class="user-badge">
          <el-icon><component :is="userTypeIcon" /></el-icon>
          <span>{{ userTypeLabel }}</span>
        </div>
        <h1 class="welcome-title">
          {{ getWelcomeMessage() }}
        </h1>
        <p class="welcome-subtitle">
          {{ getSubtitleMessage() }}
        </p>
        <!-- VIP升级提示 -->
        <div v-if="userType === 'BASIC'" class="upgrade-hint">
          <div class="upgrade-card">
            <div class="upgrade-content">
              <div class="upgrade-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="upgrade-text">
                <h4>升级VIP会员</h4>
                <p>解锁更多高级功能，享受更优质的服务体验</p>
              </div>
            </div>
            <el-button type="primary" size="default" class="upgrade-button" @click="handleUpgrade">
              <el-icon><Star /></el-icon>
              立即升级
            </el-button>
          </div>
        </div>
      </div>
      <div class="welcome-stats">
        <div class="stat-item">
          <span class="stat-number">{{ stats.totalFiles }}</span>
          <span class="stat-label">{{ t('dashboard.home.stats.fileCount') }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ formatFileSize(authStore.user?.storageUsed || 0) }}</span>
          <span class="stat-label">{{ t('dashboard.home.stats.usedSpace') }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ stats.shareCount }}</span>
          <span class="stat-label">{{ t('dashboard.home.stats.shareCount') }}</span>
        </div>
      </div>
    </div>

    <!-- 快速操作区 -->
    <div class="quick-actions-section">
      <h2 class="section-title">{{ t('dashboard.home.quickActions.title') }}</h2>
      <div class="quick-actions-grid">
        <!-- 基础功能 - 所有用户 -->
        <div class="action-card" @click="handleViewFiles">
          <div class="action-icon files">
            <el-icon><Files /></el-icon>
          </div>
          <div class="action-content">
            <h3>{{ t('dashboard.home.quickActions.fileManage.title') }}</h3>
            <p>{{ t('dashboard.home.quickActions.fileManage.description') }}</p>
          </div>
        </div>

        <div class="action-card" @click="handleShareManage">
          <div class="action-icon share">
            <el-icon><Share /></el-icon>
          </div>
          <div class="action-content">
            <h3>{{ t('dashboard.home.quickActions.shareManage.title') }}</h3>
            <p>{{ t('dashboard.home.quickActions.shareManage.description') }}</p>
          </div>
        </div>

        <!-- VIP功能 -->
        <div v-if="userType === 'VIP' || userType === 'ADMIN'" class="action-card vip-feature" @click="handleTeamManagement">
          <div class="action-icon team">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="action-content">
            <h3>{{ t('dashboard.home.quickActions.teamManagement.title') }}</h3>
            <p>{{ t('dashboard.home.quickActions.teamManagement.description') }}</p>
          </div>
          <div class="vip-badge" v-if="userType === 'VIP'">VIP</div>
          <div class="admin-badge" v-if="userType === 'ADMIN'">{{ t('dashboard.home.userTypes.admin') }}</div>
        </div>



        <!-- 管理员功能 -->
        <div v-if="userType === 'ADMIN'" class="action-card admin-feature" @click="handleUserManage">
          <div class="action-icon admin">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="action-content">
            <h3>{{ t('dashboard.home.quickActions.userManage.title') }}</h3>
            <p>{{ t('dashboard.home.quickActions.userManage.description') }}</p>
          </div>
          <div class="admin-badge">{{ t('dashboard.home.userTypes.admin') }}</div>
        </div>

        <div v-if="userType === 'ADMIN'" class="action-card admin-feature" @click="handleSystemMonitor">
          <div class="action-icon monitor">
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="action-content">
            <h3>{{ t('dashboard.home.quickActions.systemMonitor.title') }}</h3>
            <p>{{ t('dashboard.home.quickActions.systemMonitor.description') }}</p>
          </div>
          <div class="admin-badge">{{ t('dashboard.home.userTypes.admin') }}</div>
        </div>
      </div>
    </div>

    <!-- 存储概览 -->
    <div class="storage-overview-section">
      <h2 class="section-title">{{ t('dashboard.home.storageOverview.title') }}</h2>
      <div class="overview-grid">
        <div class="overview-card storage-usage">
          <div class="card-header">
            <h3>{{ t('dashboard.home.storageOverview.storageUsage.title') }}</h3>
            <span class="usage-percentage">{{ storageUsagePercentage }}%</span>
          </div>
          <div class="storage-bar">
            <div class="storage-used" :style="{ width: storageUsagePercentage + '%' }"></div>
          </div>
          <div class="storage-info">
            <span>{{ formatFileSize(authStore.user?.storageUsed || 0) }} / {{ formatFileSize(authStore.user?.storageQuota || 10737418240) }}</span>
          </div>
        </div>



        <!-- 管理员用户显示系统统计 -->
        <div v-if="userType === 'ADMIN'" class="overview-card system-stats">
          <div class="card-header">
            <h3>{{ t('dashboard.home.storageOverview.systemStats.title') }}</h3>
          </div>
          <div class="system-info">
            <div class="stat-row">
              <span>{{ t('dashboard.home.storageOverview.systemStats.totalUsers') }}</span>
              <span>1,234</span>
            </div>
            <div class="stat-row">
              <span>{{ t('dashboard.home.storageOverview.systemStats.onlineUsers') }}</span>
              <span>89</span>
            </div>
            <div class="stat-row">
              <span>{{ t('dashboard.home.storageOverview.systemStats.systemLoad') }}</span>
              <span>12%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件管理区域 -->
    <div class="files-section">
      <div class="files-grid">
        <!-- 最近文件 -->
        <div class="files-card">
          <div class="card-header">
            <h3>{{ t('dashboard.home.files.recentFiles.title') }}</h3>
            <el-link type="primary" @click="handleViewAllFiles">{{ t('dashboard.home.files.recentFiles.viewAll') }}</el-link>
          </div>
          <div class="files-list">
            <div v-for="file in recentFiles.slice(0, 5)" :key="file.id" class="file-item" @click="handleFileClick(file)">
              <div class="file-icon">
                <el-icon>
                  <component :is="getFileIcon(file.mimeType)" />
                </el-icon>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.originalName }}</div>
                <div class="file-meta">
                  <span>{{ formatFileSize(file.fileSize) }}</span>
                  <span>{{ formatDate(file.updatedAt) }}</span>
                </div>
              </div>
            </div>
            <div v-if="recentFiles.length === 0" class="empty-state">
              <el-icon><Document /></el-icon>
              <span>{{ t('dashboard.home.files.recentFiles.empty') }}</span>
            </div>
          </div>
        </div>

        <!-- 收藏文件 -->
        <div class="files-card">
          <div class="card-header">
            <h3>{{ t('dashboard.home.files.favoriteFiles.title') }}</h3>
            <el-link type="primary" @click="handleViewFavorites">{{ t('dashboard.home.files.favoriteFiles.manageFavorites') }}</el-link>
          </div>
          <div class="files-list">
            <div v-for="file in favoriteFiles.slice(0, 5)" :key="file.id" class="file-item" @click="handleFileClick(file)">
              <div class="file-icon">
                <el-icon>
                  <component :is="getFileIcon(file.mimeType)" />
                </el-icon>
              </div>
              <div class="file-info">
                <div class="file-name">{{ file.originalName }}</div>
                <div class="file-meta">
                  <span>{{ formatFileSize(file.fileSize) }}</span>
                </div>
              </div>
            </div>
            <div v-if="favoriteFiles.length === 0" class="empty-state">
              <el-icon><Star /></el-icon>
              <span>{{ t('dashboard.home.files.favoriteFiles.empty') }}</span>
            </div>
          </div>
        </div>

        <!-- 文件类型分布 -->
        <div class="files-card">
          <div class="card-header">
            <h3>{{ t('dashboard.home.files.fileTypeDistribution.title') }}</h3>
          </div>
          <div class="file-type-stats">
            <div v-for="type in fileTypeStats" :key="type.name" class="type-item">
              <div class="type-info">
                <el-icon :style="{ color: type.color }">
                  <component :is="type.icon" />
                </el-icon>
                <span class="type-name">{{ getFileTypeLabel(type.name) }}</span>
              </div>
              <div class="type-data">
                <span class="type-count">{{ type.count }}</span>
                <span class="type-size">{{ formatFileSize(type.size) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>





    <!-- 管理员功能区域 -->
    <div v-if="userType === 'ADMIN'" class="admin-section">
      <h2 class="section-title">
        <el-icon><Tools /></el-icon>
        {{ t('dashboard.home.admin.title') }}
      </h2>
      <div class="admin-grid">
        <!-- 用户统计 -->
        <div class="admin-card">
          <div class="card-header">
            <h3>{{ t('dashboard.home.admin.userStats.title') }}</h3>
          </div>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-number">1,234</span>
              <span class="stat-label">{{ t('dashboard.home.admin.userStats.totalUsers') }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">89</span>
              <span class="stat-label">{{ t('dashboard.home.admin.userStats.onlineUsers') }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">156</span>
              <span class="stat-label">{{ t('dashboard.home.admin.userStats.vipUsers') }}</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click="handleUserManage">{{ t('dashboard.home.admin.userStats.userManage') }}</el-button>
          </div>
        </div>

        <!-- 系统监控 -->
        <div class="admin-card">
          <div class="card-header">
            <h3>{{ t('dashboard.home.admin.systemMonitor.title') }}</h3>
          </div>
          <div class="system-stats">
            <div class="monitor-item">
              <span>{{ t('dashboard.home.admin.systemMonitor.cpuUsage') }}</span>
              <el-progress :percentage="systemStatus.cpu" :stroke-width="6" />
            </div>
            <div class="monitor-item">
              <span>{{ t('dashboard.home.admin.systemMonitor.memoryUsage') }}</span>
              <el-progress :percentage="systemStatus.memory" :stroke-width="6" />
            </div>
            <div class="monitor-item">
              <span>{{ t('dashboard.home.admin.systemMonitor.diskUsage') }}</span>
              <el-progress :percentage="systemStatus.disk" :stroke-width="6" />
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click="handleSystemMonitor">{{ t('dashboard.home.admin.systemMonitor.detailMonitor') }}</el-button>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="admin-card">
          <div class="card-header">
            <h3>{{ t('dashboard.home.admin.systemActivity.title') }}</h3>
          </div>
          <div class="activity-list">
            <div v-for="activity in recentActivities.slice(0, 4)" :key="activity.id" class="activity-item">
              <div class="activity-icon">
                <el-icon :style="{ color: activity.color }">
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-text">{{ t(activity.text, activity.params) }}</div>
                <div class="activity-time">{{ formatDate(activity.time) }}</div>
              </div>
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click="handleViewLogs">{{ t('dashboard.home.admin.systemActivity.viewLogs') }}</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import {
  Files, Document, Share, UserFilled, Star,
  Monitor, Tools
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { FileInfo } from '@/types'
import BackgroundCanvas from '@/components/common/BackgroundCanvas.vue'
import {
  getDashboardStats,
  getRecentFiles,
  getFavoriteFiles,
  getFileTypeStats,
  getShareStats
} from '@/api/modules/dashboard'
import type { DashboardStats, FileTypeStats, ShareStats } from '@/api/modules/dashboard'

import { useVipStore } from '@/stores/vip'

const router = useRouter()
const authStore = useAuthStore()
const vipStore = useVipStore()
const { t } = useI18n()

// 用户权限相关
type UserType = 'BASIC' | 'VIP' | 'ADMIN'

const userType = computed((): UserType => {
  // 使用后端返回的userType字段
  const userTypeFromBackend = authStore.user?.userType

  // 调试：打印用户类型信息
  console.log('=== 首页用户类型检查 ===')
  console.log('后端userType:', userTypeFromBackend)
  console.log('用户对象:', authStore.user)
  console.log('========================')

  // 根据后端返回的userType字段确定用户类型
  switch (userTypeFromBackend) {
    case 'ADMIN':
      return 'ADMIN'
    case 'VIP':
      return 'VIP'
    case 'BASIC':
    default:
      return 'BASIC'
  }
})

const userTypeClass = computed(() => ({
  'basic-user': userType.value === 'BASIC',
  'vip-user': userType.value === 'VIP',
  'admin-user': userType.value === 'ADMIN'
}))

const userTypeIcon = computed(() => {
  switch (userType.value) {
    case 'VIP': return 'Star'
    case 'ADMIN': return 'Tools'
    default: return 'User'
  }
})

const userTypeLabel = computed(() => {
  switch (userType.value) {
    case 'VIP': return t('dashboard.home.userTypes.vip')
    case 'ADMIN': return t('dashboard.home.userTypes.admin')
    default: return t('dashboard.home.userTypes.basic')
  }
})

// 统计数据 - 使用动态数据
const stats = ref<DashboardStats>({
  totalFiles: 0,
  totalFolders: 0,
  shareCount: 0,
  storageUsed: 0,
  storageQuota: 0
})

// 最近文件 - 动态获取
const recentFiles = ref<FileInfo[]>([])

// 收藏文件 - 动态获取
const favoriteFiles = ref<FileInfo[]>([])

// 文件类型统计 - 动态获取
const fileTypeStats = ref<FileTypeStats[]>([])

// 分享统计 - 动态获取
const shareStats = ref<ShareStats>({
  total: 0,
  active: 0,
  expired: 0,
  disabled: 0,
  totalViews: 0
})



// 系统状态
const systemStatus = ref({
  cpu: 45,
  memory: 67,
  disk: 23
})

// 最近活动
const recentActivities = ref([
  { id: 1, text: 'dashboard.home.storageOverview.activities.upload', params: { fileName: '项目计划.pdf' }, time: '2024-01-15T10:00:00Z', icon: 'Upload', color: '#67c23a' },
  { id: 2, text: 'dashboard.home.storageOverview.activities.share', params: { folderName: '设计稿文件夹' }, time: '2024-01-15T09:30:00Z', icon: 'Share', color: '#409eff' },
  { id: 3, text: 'dashboard.home.storageOverview.activities.createFolder', params: { folderName: '会议记录文件夹' }, time: '2024-01-15T09:00:00Z', icon: 'FolderAdd', color: '#e6a23c' }
])

// 计算存储使用百分比
const storageUsagePercentage = computed(() => {
  const used = authStore.user?.storageUsed || 0
  const limit = authStore.user?.storageQuota || 10737418240 // 10GB
  return Math.round((used / limit) * 100)
})

// 获取欢迎消息
const getWelcomeMessage = () => {
  const username = authStore.user?.username || t('dashboard.home.welcome.defaultUser')
  const greeting = getGreeting()

  // 根据用户类型显示不同的欢迎消息
  switch (userType.value) {
    case 'ADMIN':
      return `${greeting}，${username} 管理员！`
    case 'VIP':
      return `${greeting}，${username} 开发者！`
    case 'BASIC':
    default:
      return `${greeting}，${username}！`
  }
}

// 获取副标题消息
const getSubtitleMessage = () => {
  switch (userType.value) {
    case 'VIP':
      return t('dashboard.home.welcome.subtitles.vip')
    case 'ADMIN':
      return t('dashboard.home.welcome.subtitles.admin')
    default:
      return t('dashboard.home.welcome.subtitles.basic')
  }
}

// 获取文件类型标签
const getFileTypeLabel = (typeName: string) => {
  return t(`dashboard.home.files.fileTypeDistribution.types.${typeName}`)
}



// 获取问候语
const getGreeting = (): string => {
  const hour = new Date().getHours()
  if (hour < 6) return t('dashboard.home.greetings.early')
  if (hour < 9) return t('dashboard.home.greetings.morning')
  if (hour < 12) return t('dashboard.home.greetings.forenoon')
  if (hour < 14) return t('dashboard.home.greetings.noon')
  if (hour < 18) return t('dashboard.home.greetings.afternoon')
  if (hour < 22) return t('dashboard.home.greetings.evening')
  return t('dashboard.home.greetings.early')
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return t('dashboard.home.files.today')
  if (days === 1) return t('dashboard.home.files.yesterday')
  if (days < 7) return t('dashboard.home.files.daysAgo', { days })
  return date.toLocaleDateString('zh-CN')
}

// 获取文件图标
const getFileIcon = (mimeType?: string) => {
  if (!mimeType) return 'Document'
  if (mimeType.startsWith('image/')) return 'Picture'
  if (mimeType.startsWith('video/')) return 'VideoPlay'
  return 'Document'
}

// 快速操作处理
const handleViewFiles = () => {
  router.push('/dashboard/files')
}

const handleShareManage = () => {
  router.push('/dashboard/shared')
}

const handleUpgrade = () => {
  // 直接跳转到VIP会员中心页面
  router.push('/dashboard/vip-center')
}





// 管理员功能处理
const handleUserManage = () => {
  // 用户管理功能暂未实现
  ElMessage.warning('用户管理功能正在开发中，敬请期待')
}

const handleSystemMonitor = () => {
  // 系统监控功能暂未实现
  ElMessage.warning('系统监控功能正在开发中，敬请期待')
}

// 文件相关方法
const handleViewFavorites = () => {
  router.push('/dashboard/favorites')
}

// 团队协作方法
const handleTeamManagement = () => {
  router.push('/dashboard/team')
}

// 管理员功能方法
const handleViewLogs = () => {
  // 系统日志功能暂未实现
  ElMessage.warning('系统日志功能正在开发中，敬请期待')
}

// 文件操作处理
const handleFileClick = (file: FileInfo) => {
  router.push('/dashboard/files')
  ElMessage.info(t('dashboard.home.messages.viewFile', { fileName: file.originalName }))
}

const handleViewAllFiles = () => {
  router.push('/dashboard/files')
}



// 加载数据 - 使用动态API，带降级方案
const loadData = async () => {
  try {
    console.log('🚀 开始加载首页数据')

    // 逐个加载数据，避免一个失败影响全部
    await loadStatsData()
    await loadRecentFilesData()
    await loadFavoriteFilesData()
    await loadFileTypeStatsData()
    await loadShareStatsData()

    console.log('✅ 首页数据加载完成')
  } catch (error) {
    console.error('❌ 加载首页数据失败:', error)
    ElMessage.error('加载首页数据失败，请刷新页面重试')
  }
}

// 加载统计数据
const loadStatsData = async () => {
  try {
    const response = await getDashboardStats()
    if (response.code === 200) {
      stats.value = response.data
      console.log('✅ 统计数据加载成功:', response.data)
    }
  } catch (error) {
    console.warn('⚠️ 统计数据加载失败，使用默认值:', error)
    // 使用默认值
    stats.value = {
      totalFiles: 0,
      totalFolders: 0,
      shareCount: 0,
      storageUsed: 0,
      storageQuota: 10737418240
    }
  }
}

// 加载最近文件
const loadRecentFilesData = async () => {
  try {
    const response = await getRecentFiles(5)
    if (response.code === 200) {
      recentFiles.value = response.data
      console.log('✅ 最近文件加载成功:', response.data.length, '个文件')
    }
  } catch (error) {
    console.warn('⚠️ 最近文件加载失败:', error)
    recentFiles.value = []
  }
}

// 加载收藏文件
const loadFavoriteFilesData = async () => {
  try {
    const response = await getFavoriteFiles(5)
    if (response.code === 200) {
      favoriteFiles.value = response.data
      console.log('✅ 收藏文件加载成功:', response.data.length, '个文件')
    }
  } catch (error) {
    console.warn('⚠️ 收藏文件加载失败:', error)
    favoriteFiles.value = []
  }
}

// 加载文件类型统计
const loadFileTypeStatsData = async () => {
  try {
    const response = await getFileTypeStats()
    if (response.code === 200) {
      fileTypeStats.value = response.data
      console.log('✅ 文件类型统计加载成功:', response.data.length, '种类型')
    }
  } catch (error) {
    console.warn('⚠️ 文件类型统计加载失败，使用默认值:', error)
    // 使用默认值
    fileTypeStats.value = [
      { name: 'documents', count: 0, icon: 'Document', color: '#409eff', size: 0 },
      { name: 'images', count: 0, icon: 'Picture', color: '#67c23a', size: 0 },
      { name: 'videos', count: 0, icon: 'VideoPlay', color: '#e6a23c', size: 0 },
      { name: 'others', count: 0, icon: 'Files', color: '#909399', size: 0 }
    ]
  }
}

// 加载分享统计
const loadShareStatsData = async () => {
  try {
    const response = await getShareStats()
    if (response.code === 200) {
      shareStats.value = response.data
      console.log('✅ 分享统计加载成功:', response.data)
    }
  } catch (error) {
    console.warn('⚠️ 分享统计加载失败，使用默认值:', error)
    // 使用默认值
    shareStats.value = {
      total: 0,
      active: 0,
      expired: 0,
      disabled: 0,
      totalViews: 0
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.home-view {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--bg-color-primary);
  min-height: calc(100vh - 60px);
  transition: background-color 0.3s ease;
  position: relative;
  overflow: hidden;
}

// 欢迎区域
.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
  position: relative;
  overflow: hidden;
  z-index: 1;

  // 基础用户样式
  &.basic-user {
    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  }

  // VIP用户样式
  &.vip-user {
    background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 100px;
      height: 100px;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="rgba(255,255,255,0.1)"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>') no-repeat center;
      background-size: contain;
    }
  }

  // 管理员用户样式
  &.admin-user {
    background: linear-gradient(135deg, #4a7c59 0%, #5d8a6b 50%, #67c23a 100%);
    box-shadow: 0 8px 32px rgba(103, 194, 58, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;

    // 径向渐变叠加
    &::before {
      content: '';
      position: absolute;
      top: -50px;
      right: -50px;
      width: 120px;
      height: 120px;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="rgba(255,255,255,0.15)"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/></svg>') no-repeat center;
      background-size: contain;
      animation: float 6s ease-in-out infinite;
    }

    // 齿轮装饰
    &::after {
      content: '';
      position: absolute;
      bottom: -30px;
      left: -30px;
      width: 80px;
      height: 80px;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="rgba(255,255,255,0.1)"><path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/></svg>') no-repeat center;
      background-size: contain;
      animation: rotate 20s linear infinite;
    }

    // 几何装饰圆形
    .welcome-content::before {
      content: '';
      position: absolute;
      top: 20%;
      right: 15%;
      width: 60px;
      height: 60px;
      border: 2px solid rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      animation: pulse 4s ease-in-out infinite;
    }
  }

  .welcome-content {
    position: relative;
    z-index: 2;

    .user-badge {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      background: rgba(255, 255, 255, 0.2);
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
      backdrop-filter: blur(10px);
    }

    .welcome-title {
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .welcome-subtitle {
      font-size: 16px;
      opacity: 0.9;
      margin: 0 0 16px 0;
      line-height: 1.5;
    }

    .upgrade-hint {
      margin-top: 20px;
      
      .upgrade-card {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 20px;
        backdrop-filter: blur(10px);
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
        
        .upgrade-content {
          display: flex;
          align-items: center;
          gap: 16px;
          flex: 1;
          
          .upgrade-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #FFD700;
            
            .el-icon {
              animation: sparkle 2s ease-in-out infinite;
            }
          }
          
          .upgrade-text {
            flex: 1;
            
            h4 {
              margin: 0 0 4px 0;
              font-size: 16px;
              font-weight: 600;
              color: white;
            }
            
            p {
              margin: 0;
              font-size: 14px;
              color: rgba(255, 255, 255, 0.8);
              line-height: 1.4;
            }
          }
        }
        
        .upgrade-button {
          background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
          border: none;
          color: #333;
          font-weight: 600;
          padding: 12px 24px;
          border-radius: 8px;
          transition: all 0.3s ease;
          
          &:hover {
            background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
          }
          
          .el-icon {
            margin-right: 6px;
          }
        }
      }
    }
  }

  .welcome-stats {
    display: flex;
    gap: 32px;

    .stat-item {
      text-align: center;

      .stat-number {
        display: block;
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 4px;
        color: #ffffff; /* 白色数字，在橙色背景上清晰 */
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* 添加阴影增强对比 */
      }

      .stat-label {
        font-size: 14px;
        color: #ffffff; /* 白色标签，在橙色背景上清晰 */
        opacity: 0.95;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 添加阴影增强对比 */
      }
    }
  }
}

// 管理员欢迎区域动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(5deg);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.6;
  }
}

@keyframes sparkle {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.8;
  }
}

// 快速操作区域
.quick-actions-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color-primary);
    margin: 0 0 20px 0;
    transition: color 0.3s ease;
  }

  .quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .action-card {
      background-color: var(--bg-color-tertiary);
      border-radius: 12px;
      padding: 24px;
      border: 1px solid var(--border-color-light);
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 16px;
      position: relative;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-base);
        border-color: var(--theme-primary);
      }

      // VIP功能卡片
      &.vip-feature {
        border: 1px solid rgba(245, 108, 108, 0.3);
        background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(230, 162, 60, 0.05) 100%);

        &:hover {
          border-color: #f56c6c;
          box-shadow: 0 8px 25px rgba(245, 108, 108, 0.15);
        }

        .vip-badge {
          position: absolute;
          top: 8px;
          right: 8px;
          background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%);
          color: white;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 10px;
          font-weight: 600;
        }
      }

      // 管理员功能卡片
      &.admin-feature {
        border: 1px solid rgba(103, 194, 58, 0.3);
        background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, rgba(82, 155, 46, 0.05) 100%);

        &:hover {
          border-color: #67c23a;
          box-shadow: 0 8px 25px rgba(103, 194, 58, 0.15);
        }

        .admin-badge {
          position: absolute;
          top: 8px;
          right: 8px;
          background: linear-gradient(135deg, #67c23a 0%, #529b2e 100%);
          color: white;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 10px;
          font-weight: 600;
        }
      }

      .action-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;

        &.upload {
          background-color: rgba(103, 194, 58, 0.1);
          color: #67c23a;
        }

        &.folder {
          background-color: rgba(230, 162, 60, 0.1);
          color: #e6a23c;
        }

        &.files {
          background-color: rgba(64, 158, 255, 0.1);
          color: #409eff;
        }

        &.search {
          background-color: rgba(139, 92, 246, 0.1);
          color: #8b5cf6;
        }

        &.team {
          background-color: rgba(245, 108, 108, 0.1);
          color: #f56c6c;
        }
      }

      .action-content {
        h3 {
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 4px 0;
          color: var(--text-color-primary);
          transition: color 0.3s ease;
        }

        p {
          font-size: 14px;
          color: var(--text-color-secondary);
          margin: 0;
          transition: color 0.3s ease;
        }
      }
    }
  }
}

// 存储概览区域
.storage-overview-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color-primary);
    margin: 0 0 20px 0;
    transition: color 0.3s ease;
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    .overview-card {
      background-color: var(--bg-color-tertiary);
      border-radius: 12px;
      padding: 24px;
      border: 1px solid var(--border-color-light);
      transition: all 0.3s ease;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-color-primary);
          margin: 0;
          transition: color 0.3s ease;
        }

        .usage-percentage {
          font-size: 14px;
          font-weight: 600;
          color: var(--theme-primary);
        }

        .count-badge {
          background-color: var(--theme-primary);
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }
      }

      &.storage-usage {
        .storage-bar {
          height: 8px;
          background-color: var(--bg-color-secondary);
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 12px;

          .storage-used {
            height: 100%;
            background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
            transition: width 0.3s ease;
          }
        }

        .storage-info {
          font-size: 14px;
          color: var(--text-color-secondary);
          transition: color 0.3s ease;
        }
      }

      &.file-types {
        .file-type-list {
          .file-type-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color-light);

            &:last-child {
              border-bottom: none;
            }

            .type-info {
              display: flex;
              align-items: center;
              gap: 8px;

              .type-name {
                font-size: 14px;
                color: var(--text-color-primary);
                transition: color 0.3s ease;
              }
            }

            .type-count {
              font-size: 14px;
              font-weight: 500;
              color: var(--text-color-secondary);
              transition: color 0.3s ease;
            }
          }
        }
      }

      &.recent-activity {
        .activity-list {
          .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color-light);

            &:last-child {
              border-bottom: none;
            }

            .activity-icon {
              width: 32px;
              height: 32px;
              border-radius: 8px;
              background-color: var(--bg-color-secondary);
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              flex-shrink: 0;
            }

            .activity-content {
              flex: 1;

              .activity-text {
                font-size: 14px;
                color: var(--text-color-primary);
                margin-bottom: 4px;
                transition: color 0.3s ease;
              }

              .activity-time {
                font-size: 12px;
                color: var(--text-color-secondary);
                transition: color 0.3s ease;
              }
            }
          }
        }
      }
    }
  }
}

// 文件区域
.files-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;

  .files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;

    .files-card {
      background-color: var(--bg-color-tertiary);
      border-radius: 12px;
      padding: 24px;
      border: 1px solid var(--border-color-light);
      transition: all 0.3s ease;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-color-primary);
          margin: 0;
          transition: color 0.3s ease;
        }
      }

      .files-list {
        .file-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 0;
          border-bottom: 1px solid var(--border-color-light);
          cursor: pointer;
          transition: all 0.3s ease;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: var(--bg-color-secondary);
            margin: 0 -12px;
            padding: 12px;
            border-radius: 8px;
          }

          .file-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background-color: var(--bg-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: var(--theme-primary);
            flex-shrink: 0;
          }

          .file-info {
            flex: 1;
            min-width: 0;

            .file-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--text-color-primary);
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              transition: color 0.3s ease;
            }

            .file-meta {
              display: flex;
              gap: 12px;
              font-size: 12px;
              color: var(--text-color-secondary);
              transition: color 0.3s ease;
            }
          }
        }

        .empty-state {
          text-align: center;
          padding: 40px 20px;
          color: var(--text-color-secondary);

          .el-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
          }

          span {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 团队协作区域
.collaboration-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color-primary);
    margin: 0 0 20px 0;
    transition: color 0.3s ease;
  }

  .collaboration-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;

    .collab-card {
      background-color: var(--bg-color-tertiary);
      border-radius: 12px;
      padding: 24px;
      border: 1px solid var(--border-color-light);
      transition: all 0.3s ease;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-color-primary);
          margin: 0;
          transition: color 0.3s ease;
        }

        .count-badge {
          background-color: var(--theme-primary);
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .shared-folders {
        .folder-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 0;
          border-bottom: 1px solid var(--border-color-light);
          cursor: pointer;
          transition: all 0.3s ease;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: var(--bg-color-secondary);
            margin: 0 -12px;
            padding: 12px;
            border-radius: 8px;
          }

          .folder-icon {
            font-size: 20px;
            color: var(--color-warning);
          }

          .folder-name {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color-primary);
            transition: color 0.3s ease;
          }

          .member-count {
            font-size: 12px;
            color: var(--text-color-secondary);
            transition: color 0.3s ease;
          }
        }
      }

      .team-members {
        .member-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 0;
          border-bottom: 1px solid var(--border-color-light);

          &:last-child {
            border-bottom: none;
          }

          .member-info {
            flex: 1;

            .member-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--text-color-primary);
              margin-bottom: 4px;
              transition: color 0.3s ease;
            }

            .member-status {
              font-size: 12px;
              transition: color 0.3s ease;

              &.online {
                color: var(--color-success);
              }

              &.offline {
                color: var(--text-color-secondary);
              }

              &.busy {
                color: var(--color-warning);
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home-view {
    padding: 16px;
  }

  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;

    .welcome-stats {
      justify-content: center;
    }
    
    .upgrade-hint .upgrade-card {
      flex-direction: column;
      text-align: center;
      gap: 16px;
      
      .upgrade-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
      }
      
      .upgrade-button {
        align-self: stretch;
      }
    }
  }

  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .files-grid {
    grid-template-columns: 1fr;
  }

  .collaboration-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .welcome-stats {
    flex-direction: column;
    gap: 16px;
  }
}

// 简化样式
.files-section {
  margin-bottom: 32px;
}

.files-grid, .collaboration-grid, .developer-grid, .admin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

@media (max-width: 768px) {
  .files-grid, .collaboration-grid, .developer-grid, .admin-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.files-card, .collab-card, .dev-card, .admin-card {
  background-color: var(--el-bg-color-page);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.count-badge {
  background-color: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.file-item, .member-item, .folder-item, .project-item, .repo-item, .api-item, .activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.file-item:hover, .folder-item:hover {
  background-color: var(--el-bg-color);
}

.file-icon, .project-icon, .repo-icon, .api-icon, .activity-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  color: var(--el-color-primary);
}

.file-info, .member-info, .folder-info, .project-info, .repo-info, .api-info, .activity-content {
  flex: 1;
  min-width: 0;
}

.file-name, .member-name, .folder-name, .project-name, .repo-name, .api-name, .activity-text {
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta, .member-status, .member-count, .project-status, .repo-meta, .api-version, .activity-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
}

.stat-value.active {
  color: #67c23a;
}

.stat-value.expired {
  color: #f56c6c;
}

.card-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-light);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
}

.empty-state .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.collaboration-section, .developer-section, .admin-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.developer-section .section-title {
  color: #f56c6c;
}

.admin-section .section-title {
  color: #67c23a;
}

.dev-card {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(230, 162, 60, 0.05) 100%);
  border: 1px solid rgba(245, 108, 108, 0.2);
}

.admin-card {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, rgba(82, 155, 46, 0.05) 100%);
  border: 1px solid rgba(103, 194, 58, 0.2);
}

.user-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  text-align: center;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff; /* 白色数字，在橙色背景上清晰 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* 添加阴影增强对比 */
}

.stat-label {
  font-size: 12px;
  color: #ffffff; /* 白色标签，在橙色背景上清晰 */
  margin-top: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 添加阴影增强对比 */
}

.monitor-item {
  margin-bottom: 12px;
}

.monitor-item span {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}


</style>
