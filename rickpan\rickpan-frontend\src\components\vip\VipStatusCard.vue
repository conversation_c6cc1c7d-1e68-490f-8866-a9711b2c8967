<template>
  <el-card class="vip-status-card" :class="{ 'vip-card': isVip, 'dark': isDark }">
    <template #header>
      <div class="card-header">
        <div class="user-info">
          <el-icon class="user-icon" :class="{ 'vip-icon': isVip }">
            <User />
          </el-icon>
          <div>
            <span class="user-type">{{ userTypeText }}</span>
            <el-tag v-if="isVip" type="warning" size="small" class="vip-tag">VIP</el-tag>
          </div>
        </div>
        <el-button 
          v-if="!isVip" 
          type="primary" 
          size="small" 
          @click="showUpgrade"
          class="upgrade-btn"
        >
          {{ t('dashboard.vipCenter.statusCard.upgradeButton') }}
        </el-button>
      </div>
    </template>

    <div class="status-content">
      <!-- VIP状态信息 -->
      <div v-if="isVip" class="vip-info">
        <div class="expire-info">
          <el-icon><Calendar /></el-icon>
          <span>{{ t('dashboard.vipCenter.statusCard.expireTime', { time: formatDate(vipStatus?.vipExpireTime) }) }}</span>
        </div>
        <div class="days-left" :class="expirationStatusClass">
          <el-icon><Clock /></el-icon>
          <span>{{ formatDaysLeft(daysLeft) }}</span>
          <el-tag 
            v-if="isExpiringSoon" 
            type="warning" 
            size="small"
            class="warning-tag"
          >
            {{ t('dashboard.vipCenter.statusCard.expiringSoon') }}
          </el-tag>
        </div>
      </div>

      <!-- 存储使用情况 -->
      <div class="storage-info">
        <div class="storage-header">
          <el-icon><FolderOpened /></el-icon>
          <span>{{ t('dashboard.vipCenter.statusCard.storageSpace') }}</span>
        </div>
        <div class="storage-progress">
          <el-progress 
            :percentage="storageUsagePercentage" 
            :status="storageStatus"
            :stroke-width="8"
          />
          <div class="storage-text">
            {{ formatFileSize(vipStatus?.storageUsed || 0) }} / 
            {{ formatFileSize(vipStatus?.storageQuota || 0) }}
          </div>
        </div>
      </div>

      <!-- 功能使用统计 -->
      <div v-if="featureUsages.length > 0" class="feature-usage">
        <div class="section-title">
          <el-icon><DataAnalysis /></el-icon>
          <span>{{ t('dashboard.vipCenter.statusCard.featureUsage') }}</span>
        </div>
        <div class="usage-list">
          <div 
            v-for="usage in featureUsages" 
            :key="usage.featureCode" 
            class="usage-item"
          >
            <div class="usage-info">
              <span class="feature-name">{{ usage.featureName }}</span>
              <span class="usage-count">
                {{ usage.used }} / {{ usage.limit === -1 ? t('dashboard.vipCenter.statusCard.unlimited') : usage.limit }}
              </span>
            </div>
            <el-progress 
              :percentage="usage.percentage" 
              :status="usage.status"
              :stroke-width="6"
              :show-text="false"
            />
          </div>
        </div>
      </div>

      <!-- 非VIP用户的提示 -->
      <div v-if="!isVip" class="basic-user-tips">
        <el-alert
          :title="t('dashboard.vipCenter.statusCard.upgradeVipTips')"
          type="info"
          :closable="false"
          show-icon
        >
          <ul class="benefits-list">
            <li v-for="benefit in benefits" :key="benefit">{{ benefit }}</li>
          </ul>
        </el-alert>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { 
  User, 
  Calendar, 
  Clock, 
  FolderOpened, 
  DataAnalysis 
} from '@element-plus/icons-vue'
import { useVipStore } from '@/stores/vip'
import { useThemeStore } from '@/stores/theme'

const { t } = useI18n()
const vipStore = useVipStore()
const themeStore = useThemeStore()

// 计算当前是否为暗色模式
const isDark = computed(() => themeStore.isDark)

// 计算属性
const isVip = computed(() => vipStore.isVip)
const vipStatus = computed(() => vipStore.vipStatus)
const featureUsages = computed(() => vipStore.featureUsages)
const daysLeft = computed(() => vipStore.daysLeft)
const storageUsagePercentage = computed(() => vipStore.storageUsagePercentage)
const storageStatus = computed(() => vipStore.storageStatus)
const isExpiringSoon = computed(() => vipStore.isExpiringSoon)

const userTypeText = computed(() => {
  return isVip.value ? t('dashboard.vipCenter.statusCard.userTypes.vip') : t('dashboard.vipCenter.statusCard.userTypes.basic')
})

const benefits = computed(() => {
  const benefitsData = t('dashboard.vipCenter.statusCard.benefits')
  return Array.isArray(benefitsData) ? benefitsData : []
})

const expirationStatusClass = computed(() => {
  if (daysLeft.value <= 3) return 'critical'
  if (daysLeft.value <= 7) return 'warning'
  return 'normal'
})

// 方法
const showUpgrade = () => {
  vipStore.showUpgradeDialog()
}

const formatDate = (dateString?: string) => {
  return vipStore.formatDate(dateString || '')
}

const formatDaysLeft = (days: number) => {
  return vipStore.formatDaysLeft(days)
}

const formatFileSize = (bytes: number | string) => {
  return vipStore.formatFileSize(bytes)
}
</script>

<style scoped>
.vip-status-card {
  /* margin-bottom: 20px; */
  transition: all 0.2s ease;
}

.vip-status-card.dark {
  background: #1e293b;
  border-color: #334155;
}

.vip-card {
  border: 2px solid #f59e0b;
  background: #fef3c7;
}

.vip-status-card.dark.vip-card {
  border: 2px solid #f59e0b;
  background: #451a03;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-icon {
  font-size: 24px;
  color: #9ca3af;
  transition: color 0.2s ease;
}

.vip-status-card.dark .user-icon {
  color: #6b7280;
}

.user-icon.vip-icon {
  color: #f59e0b;
}

.user-type {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  transition: color 0.2s ease;
}

.vip-status-card.dark .user-type {
  color: #f1f5f9;
}

.vip-tag {
  margin-left: 8px;
}

.upgrade-btn {
  background: #f59e0b;
  border: none;
  transition: background-color 0.2s ease;
}

.upgrade-btn:hover {
  background: #d97706;
}

.status-content {
  padding: 10px 0;
}

.vip-info {
  margin-bottom: 20px;
  padding: 15px;
  background: #f9fafb;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.vip-status-card.dark .vip-info {
  background: #0f172a;
}

.expire-info,
.days-left {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.days-left.critical {
  color: #f56c6c;
}

.days-left.warning {
  color: #e6a23c;
}

.days-left.normal {
  color: #67c23a;
}

.warning-tag {
  margin-left: 8px;
}

.storage-info {
  margin-bottom: 20px;
}

.storage-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-weight: 600;
  color: #111827;
  transition: color 0.2s ease;
}

.vip-status-card.dark .storage-header {
  color: #f1f5f9;
}

.storage-progress {
  margin-bottom: 8px;
}

.storage-text {
  text-align: center;
  font-size: 12px;
  color: #9ca3af;
  margin-top: 5px;
  transition: color 0.2s ease;
}

.vip-status-card.dark .storage-text {
  color: #6b7280;
}

.feature-usage {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #111827;
  transition: color 0.2s ease;
}

.vip-status-card.dark .section-title {
  color: #f1f5f9;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.usage-item {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.vip-status-card.dark .usage-item {
  background: #0f172a;
}

.usage-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.feature-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  transition: color 0.2s ease;
}

.vip-status-card.dark .feature-name {
  color: #f1f5f9;
}

.usage-count {
  font-size: 12px;
  color: #9ca3af;
  transition: color 0.2s ease;
}

.vip-status-card.dark .usage-count {
  color: #6b7280;
}

.basic-user-tips {
  margin-top: 20px;
}

.benefits-list {
  margin: 10px 0 0 20px;
  padding: 0;
}

.benefits-list li {
  margin-bottom: 5px;
  color: #6b7280;
  font-size: 14px;
  transition: color 0.2s ease;
}

.vip-status-card.dark .benefits-list li {
  color: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .usage-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>